﻿
using DWH.ProjectServices.API.Infrastructure.Persistence.Configurations;
using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore;

namespace DWH.ProjectServices.API.Infrastructure.Persistence;

public class OracleDbContext : DbContext
{
    public OracleDbContext(DbContextOptions<OracleDbContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfiguration(new ProductGroupConfigurations());
        modelBuilder.ApplyConfiguration(new ProjectSubTypeConfigurations());
        modelBuilder.ApplyConfiguration(new PeriodicityConfigurations());
        modelBuilder.ApplyConfiguration(new DomainProductGroupConfigurations());
        modelBuilder.ApplyConfiguration(new ProductionBaseProjectConfigurations());
        modelBuilder.ApplyConfiguration(new ReportingBaseProjectConfigurations());
        modelBuilder.ApplyConfiguration(new RBBaseProjectConfigurations());
        modelBuilder.ApplyConfiguration(new QCStatusConfigurations());
        modelBuilder.ApplyConfiguration(new ProductionProjectConfigurations());
        modelBuilder.ApplyConfiguration(new ReportingProjectConfigurations());
        modelBuilder.ApplyConfiguration(new RBProjectConfigurations());
        modelBuilder.ApplyConfiguration(new FactPdOutItmConfigurations());
        modelBuilder.ApplyConfiguration(new PGProjectConfigurations());
        modelBuilder.ApplyConfiguration(new RBProjectDomainPGConfigurations());
        modelBuilder.ApplyConfiguration(new PanelTypeConfigurations());
    }
    public DbSet<ProductGroup> ProductGroups { get; set; }

    public DbSet<ProductionBaseProject> ProductionBaseProjects { get; set; }
    public DbSet<ReportingBaseProject> ReportingBaseProjects { get; set; }
    public DbSet<RBBaseProject> RBBaseProjects { get; set; }

    public DbSet<ProjectSubType> ProjectSubTypes { get; set; }

    public DbSet<Periodicity> PeriodicityDesc { get; set; }
    public DbSet<DomainProductGroup> DomainProductGroups { get; set; }
    public DbSet<QCStatus> QCStatus { get; set; }
    public DbSet<ProductionProject> ProductionProjects { get; set; }
    public DbSet<ReportingProject> ReportingProjects { get; set; }
    public DbSet<RBProject> RBProjects { get; set; }
    public DbSet<FactPdOutItm> FactPdOutItms { get; set; }
    public DbSet<PGProject> PGProjects { get; set; }
    public DbSet<RBProjectDomainPG> RBProjectDomainPGs { get; set; }
    public DbSet<BaseProjectPanelType> BaseProjectPanels { get; set; }


}