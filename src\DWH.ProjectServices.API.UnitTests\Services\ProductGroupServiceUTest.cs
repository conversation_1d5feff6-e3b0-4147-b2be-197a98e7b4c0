﻿using AutoFixture;
using AutoMapper;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Infrastructure.Persistence.Profile;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Models.Dtos;
using DWH.ProjectServices.API.Services;
using FluentAssertions;
using Moq;

namespace DWH.ProjectServices.API.UnitTests.Services;

public class ProductGroupServiceUTest
{
    private readonly IMapper _mapper;
    private readonly IFixture _fixture;
    private readonly Mock<IProductGroupRepository> _productGroupRepositoryStub;
    private readonly ProductGroupService _service;

    public ProductGroupServiceUTest()
    {
        _fixture = new Fixture();
        var mappingConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new ProductGroupProfile());
        });
        _mapper = mappingConfig.CreateMapper();
        _productGroupRepositoryStub = new Mock<IProductGroupRepository>();
        _service = new ProductGroupService(_mapper, _productGroupRepositoryStub.Object);
    }

    [Fact]
    public async Task GetAllAsync_When_ProductGroupsNotExist_Expect_EmptyList()
    {
        // Arrange
        var expectedResult = Array.Empty<ProductGroup>();

        _productGroupRepositoryStub
            .Setup(pr => pr.GetAllAsync(It.IsAny<ProductGroupRequest>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _service.GetAllAsync(It.IsAny<ProductGroupRequest>());

        // Assert
        result.Should().NotBeNull().And.BeEquivalentTo(expectedResult);
    }


    [Fact]
    public async Task GetAllAsync_When_ProductGroupsExist_Expect_ListOfProductGroups()
    {
        // Arrange
        var expectedResult = _fixture.Create<IEnumerable<ProductGroup>>();

        var productGroupRequestDto = _fixture.Create<ProductGroupRequest>();

        _productGroupRepositoryStub
            .Setup(pr => pr.GetAllAsync(It.IsAny<ProductGroupRequest>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _service.GetAllAsync(productGroupRequestDto);

        // Assert
        var response = _mapper.Map<ICollection<ProductGroupResponse>>(expectedResult);
        result.Should().BeEquivalentTo(response);
    }

    [Fact]
    public async Task GetDescriptionAsync_When_Called_Expect_CorrectDescriptionResponse()
    {
        // Arrange
        var productGroupDto = _fixture.Create<DescriptionRequest>();
        var expectedPeriodicityDesc = _fixture.Create<string>();
        var expectedDomainProductGroups = _fixture.Create<IEnumerable<DomainProductGroup>>();

        _productGroupRepositoryStub
            .Setup(repo => repo.GetPeriodicityDesc(productGroupDto.PeriodicityId))
            .Returns(expectedPeriodicityDesc);

        _productGroupRepositoryStub
            .Setup(repo => repo.GetDomainProductGroupAsync(productGroupDto))
            .ReturnsAsync(expectedDomainProductGroups);

        var expectedResponse = new DescriptionResponse(expectedPeriodicityDesc, expectedDomainProductGroups);

        // Act
        var result = await _service.GetDescriptionAsync(productGroupDto);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(expectedResponse);
    }

    [Fact]
    public async Task GetAllAsync_WhenCalled_AndRepositoryThrowsException_ThrowsException()
    {
        // Arrange
        var requestDto = new ProductGroupRequest();

        _productGroupRepositoryStub
            .Setup(repo => repo.GetAllAsync(requestDto))
            .ThrowsAsync(new EntityNotExistsException($"ProductGroup Not Found "));

        // Act
        var act = async () => await _service.GetAllAsync(requestDto);

        // Assert
        await act.Should().ThrowAsync<EntityNotExistsException>();
    }

    [Fact]
    public async Task GetDescriptionAsync_WhenCalled_AndRepositoryThrowsException_ThrowsException()
    {
        // Arrange
        var requestDto = new DescriptionRequest(1, new[] { 1, 2 });

        _productGroupRepositoryStub
            .Setup(repo => repo.GetPeriodicityDesc(requestDto.PeriodicityId))
            .Throws(new EntityNotExistsException($"Periodicity data Not Found "));

        // Act
        var act = async () => await _service.GetDescriptionAsync(requestDto);

        // Assert
        await act.Should().ThrowAsync<EntityNotExistsException>();
    }

}
