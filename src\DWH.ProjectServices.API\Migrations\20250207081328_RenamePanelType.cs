﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DWH.ProjectServices.API.Migrations;

/// <inheritdoc />
public partial class RenamePanelType : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.RenameTable(name: "BaseProject_PanelType", newName: "BaseProject_DataType");

        migrationBuilder.RenameColumn(
            name: "PanelTypeId",
            table: "BaseProject",
            newName: "DataTypeId");

        migrationBuilder.RenameIndex(
            name: "IX_BaseProject_PanelTypeId",
            table: "BaseProject",
            newName: "IX_BaseProject_DataTypeId");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_BaseProject_BaseProject_DataType_DataTypeId",
            table: "BaseProject");

        migrationBuilder.DropTable(
            name: "BaseProject_DataType");

        migrationBuilder.RenameColumn(
            name: "DataTypeId",
            table: "BaseProject",
            newName: "PanelTypeId");

        migrationBuilder.RenameIndex(
            name: "IX_BaseProject_DataTypeId",
            table: "BaseProject",
            newName: "IX_BaseProject_PanelTypeId");

        migrationBuilder.AlterColumn<DateTimeOffset>(
            name: "CreatedWhen",
            table: "QCPeriod",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(2025, 1, 7, 10, 54, 4, 757, DateTimeKind.Unspecified).AddTicks(4523), new TimeSpan(0, 0, 0, 0, 0)),
            oldClrType: typeof(DateTimeOffset),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTimeOffset(new DateTime(2025, 2, 7, 8, 13, 27, 258, DateTimeKind.Unspecified).AddTicks(5818), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AlterColumn<DateTimeOffset>(
            name: "CreatedWhen",
            table: "BaseProject",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(2025, 1, 7, 10, 54, 4, 757, DateTimeKind.Unspecified).AddTicks(2370), new TimeSpan(0, 0, 0, 0, 0)),
            oldClrType: typeof(DateTimeOffset),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTimeOffset(new DateTime(2025, 2, 7, 8, 13, 27, 258, DateTimeKind.Unspecified).AddTicks(4969), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.CreateTable(
            name: "BaseProject_PanelType",
            columns: table => new
            {
                Id = table.Column<int>(type: "integer", nullable: false)
                    .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                Desc = table.Column<string>(type: "text", nullable: true),
                Name = table.Column<string>(type: "character varying(40)", maxLength: 40, nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_BaseProject_PanelType", x => x.Id);
            });

        migrationBuilder.AddForeignKey(
            name: "FK_BaseProject_BaseProject_PanelType_PanelTypeId",
            table: "BaseProject",
            column: "PanelTypeId",
            principalTable: "BaseProject_PanelType",
            principalColumn: "Id",
            onDelete: ReferentialAction.Cascade);
    }
}
