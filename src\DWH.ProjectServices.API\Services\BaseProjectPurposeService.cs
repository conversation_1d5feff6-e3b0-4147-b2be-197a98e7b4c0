﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Services.Interfaces;

namespace DWH.ProjectServices.API.Services;

public class BaseProjectPurposeService : IBaseProjectPurposeService
{
    private readonly IBaseProjectPurposeRepository _baseprojectPurposeRepository;
    public BaseProjectPurposeService(IBaseProjectPurposeRepository baseprojectPurposeRepository)
    {
        _baseprojectPurposeRepository = baseprojectPurposeRepository;
    }
    public async Task<IReadOnlyCollection<Purposes>> GetAllAsync()
    {
        var result = await _baseprojectPurposeRepository.GetAllAsync();

        return result;
    }
}
