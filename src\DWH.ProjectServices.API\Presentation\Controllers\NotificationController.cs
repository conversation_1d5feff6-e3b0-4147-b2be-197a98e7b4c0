﻿using DWH.ProjectServices.API.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace DWH.ProjectServices.API.Presentation.Controllers;

public class NotificationController : ApiController
{
    private readonly INotificationService _notificationService;

    public NotificationController(INotificationService notificationService)
    {
        _notificationService = notificationService;
    }

    [HttpPost("send")]
    public async Task<IActionResult> SendNotification([FromHeader(Name = "Username")] string username, [FromBody] NotificationRequest request)
    {
        if (string.IsNullOrEmpty(username))
        {
            return BadRequest("Username is required in headers.");
        }

        await _notificationService.SendNotificationToUserAsync(username, request);
        return Ok(new { Status = $"Notification sent to {username}" });
    }

    public class NotificationRequest
    {
        public string Message { get; set; }
        public int RetailerSeparationRequestId { get; set; }
    }
}
