﻿namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;

public class BaseProjectListRequest
{
    public int Id { get; set; }
    public string Name { get; set; }
    public int[] CountryIds { get; set; }
    public long[] ProductGroupIds { get; set; }
    public int[] PeriodicityIds { get; set; }
    public long[] PanelIds { get; set; }
    public long[] DataTypeIds { get; set; }
    public long[] PurposeIds { get; set; }
    public int[] TypeIds { get; set; }
    public int Limit { get; set; }
    public string[] Users { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int[] BaseProjectIds { get; set; }
    public int[] QCProjectIds { get; set; }

    public bool IncludeDeleted { get; set; }
}
