﻿using DWH.ProjectServices.API.Infrastructure.RabbitMQ;
using DWH.ProjectServices.API.Services.Interfaces;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;

namespace DWH.ProjectServices.API.Services;

public class ErrorHandler : IErrorHandler
{
    private ILogger<MessageConsumer> _logger;
    private IConfiguration _configuration;

    public ErrorHandler(IConfiguration configuration, ILogger<MessageConsumer> logger)
    {
        _logger = logger;
        _configuration = configuration;
    }

    public void HandleErrorMessages(IChannel channel, BasicDeliverEventArgs ea)
    {
        channel.BasicAckAsync(ea.DeliveryTag, false);
        var deliveryCount = GetDeliveryCount(ea);
        _logger.LogInformation("Delivery Count - {deliveryCount}", deliveryCount);
        int retryMaxLimit = Convert.ToInt16(_configuration.GetSection("RetryLimit:Value").Value);
        _logger.LogInformation("RetryMaxLimit - {retryMaxLimit}", retryMaxLimit);
        channel.BasicRejectAsync(ea.DeliveryTag, deliveryCount < retryMaxLimit);
    }

    private int GetDeliveryCount(BasicDeliverEventArgs ea)
    {
        if (ea.BasicProperties.Headers != null && ea.BasicProperties.Headers["x-delivery-count"] != null)
        {
            return int.Parse(ea.BasicProperties.Headers["x-delivery-count"].ToString());
        }
        return 0;
    }
}
