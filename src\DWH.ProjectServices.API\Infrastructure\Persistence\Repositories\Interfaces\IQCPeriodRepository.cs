﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;

public interface IQCPeriodRepository
{

    Task<QCPeriod> AddAsyncQCPeriod(QCPeriod newQCPeriod);
    Task<QCPeriod> EditPeriodAsync(long qcPeriodId, QCPeriodEdits qcPeriodEditRequest);
    Task<IEnumerable<QCPeriod>> GetAllQCPeriodsAsync(int qcProjectId);
    Task<QCPeriodWithBPIdResponse> GetQCPeriodAsync(long qcPeriodId);
    Task<IReadOnlyList<ResponseInfoQCPeriod>> DeletePeriodAsync(QCPeriodDeletes qcPeriodDeleteRequest);
    Task<bool> CheckIfQCPeriodExists(int qcProjectId, long periodId);
    Task<List<long>> GetAuthorizedCountriesForPeriods(QCPeriodCountries qcPeriodCountryRequest);
}
