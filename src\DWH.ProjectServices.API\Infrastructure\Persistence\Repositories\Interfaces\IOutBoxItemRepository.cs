﻿using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;

public interface IOutBoxItemRepository
{
    Task SaveMessagesAsync(object message, ProjectMessageType messageTypeId);
    Task<List<OutBoxItemEntity>> GetUnprocessedMessagesAsync();
    Task MarkMessageAsProcessedAsync(Guid messageId);
    Task CleanupOldProcessedMessagesAsync();
}
