﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;

namespace DWH.ProjectServices.API.Models;

[Table("ResetCorrectionType")]
public class ResetCorrectionTypeEntity
{
    [Key]
    public int Id { get; set; }

    [MaxLength(40)]
    public string Name { get; set; }

    public QCProjectEntity QCProject { get; set; }

}
