﻿using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace DWH.ProjectServices.API.Presentation.Controllers;

public class BaseProjectDataTypeController : ApiController
{
    private readonly IBaseProjectDataTypeService _baseProjectDataTypeService;

    public BaseProjectDataTypeController(IBaseProjectDataTypeService baseProjectDataTypeService)
    {
        _baseProjectDataTypeService = baseProjectDataTypeService;
    }

    [HttpGet]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(IEnumerable<BaseProjectDataTypeResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetAllAsync()
    {
        var baseProjectDataTypes = await _baseProjectDataTypeService.GetAllAsync();
        var result = baseProjectDataTypes?.Select(p => new BaseProjectDataTypeResponse
        {
            Id = p.Id,
            Name = p.Name,
            Description = p.Description
        }).ToList();


        return OkOrEmptyList(result);
    }

}
