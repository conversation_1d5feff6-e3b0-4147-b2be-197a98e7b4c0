﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;

public class BaseProjectCreateRequest
{
    [MaxLength(40)]
    [Required]
    public string Name { get; set; }
    [Required]
    public int TypeId { get; set; }

    public int PanelId { get; set; }
    public bool IsRelevantForReportingEnabled { get; set; }
    public int DataTypeId { get; set; }
    public int PurposeId { get; set; }

    [Required]
    public int PeriodicityId { get; set; }
    [Required]
    public int CountryId { get; set; }
    public ICollection<BaseProjectPredecessorModelDto> Predecessors { get; set; }

    [Required]
    public ICollection<BaseProjectProductGroupModelDto> ProductGroups { get; set; }

    public int? ResetCorrectionTypeId { get; set; }

    public bool? IsAutoLoad { get; set; }

    public int? SQCMode { get; set; }

    public bool? IsAutomatedPriceCheck { get; set; }

    [JsonIgnore]
    public string CreatedBy { get; set; }

    [JsonIgnore]
    public string UpdatedBy { get; set; }

    [JsonIgnore]
    public bool Deleted { get; set; }
    public string Suffix { get; set; }

}
