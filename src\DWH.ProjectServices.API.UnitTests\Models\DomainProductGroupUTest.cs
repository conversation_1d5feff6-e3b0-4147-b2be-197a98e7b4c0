﻿using AutoFixture;
using DWH.ProjectServices.API.Models;
using FluentAssertions;

namespace DWH.ProjectServices.API.UnitTests.Models;

public class DomainProductGroupTests
{
    private readonly IFixture _fixture;

    public DomainProductGroupTests()
    {
        _fixture = new Fixture();
    }

    [Fact]
    public void When_ValidParameters_Expect_NotNullInstance()
    {
        // Arrange
        var domainProductGroupId = _fixture.Create<int>();
        var domainProductGroupDDesc = _fixture.Create<string>();
        var sectorSDesc = _fixture.Create<string>();

        // Act
        var instance = new DomainProductGroup(domainProductGroupId, domainProductGroupDDesc, sectorSDesc);

        // Assert
        instance.Should().NotBeNull();
        instance.DomainProductGroupId.Should().Be(domainProductGroupId);
        instance.DomainProductGroupDDesc.Should().Be(domainProductGroupDDesc);
        instance.SectorSDesc.Should().Be(sectorSDesc);
    }
}
