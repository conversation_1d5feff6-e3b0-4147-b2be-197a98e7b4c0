﻿using System.Text.Json.Serialization;
using DWH.ProjectServices.API.Domain.Models;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;

public class RetailerSeperationResponse
{
    public int Id { get; set; }

    public long FromPeriodId { get; set; }

    public long ToPeriodId { get; set; }

    public bool ResetCorrection { get; set; }

    public bool Extrapolation { get; set; }

    public string JiraId { get; set; }

    public ICollection<RetailerSeperation> RetailerSeperations { get; set; }

    public ICollection<RetailerSeperationRequestDetail> RetailerSeperationRequestDetails { get; set; }

    [JsonIgnore]
    public int RequestStatusId { get; set; }

    public string RequestStatus { get; set; }
}
