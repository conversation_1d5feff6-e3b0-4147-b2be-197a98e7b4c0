﻿using AutoMapper;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Profile;
using DWH.ProjectServices.API.Presentation.Controllers;
using DWH.ProjectServices.API.Services.Interfaces;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Moq;

namespace DWH.ProjectServices.API.UnitTests.Presentation.Controllers;

public class ResetCorrectionTypeControllerTests
{
    private readonly IMapper _mapper;
    private readonly Mock<IResetCorrectionTypeService> _resetCorrectionTypeServiceMock;
    private readonly ResetCorrectionTypeController _controller;

    public ResetCorrectionTypeControllerTests()
    {
        var mappingConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new BaseProjectsProfile());
        });
        _mapper = mappingConfig.CreateMapper();
        _resetCorrectionTypeServiceMock = new Mock<IResetCorrectionTypeService>();
        _controller = new ResetCorrectionTypeController(_resetCorrectionTypeServiceMock.Object, _mapper);
    }

    [Fact]
    public async Task GetAllAsync_When_CalledForNotEmptyResult_Expect_200Response()
    {
        // Arrange
        var expectedResult = new List<ResetCorrectionTypes>
        {
            new ResetCorrectionTypes{Id = 1, Name = "ResetCorrectionObject"}
        };

        _resetCorrectionTypeServiceMock
            .Setup(s => s.GetAllAsync())
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAllAsync();

        // Assert
        result.Should().BeOfType<OkObjectResult>()
            .Which.Value.Should().BeEquivalentTo(expectedResult);
    }

    [Fact]
    public async Task GetAllAsync_When_ServiceReturnsNull_Expect_404NotFound()
    {
        // Arrange
        IReadOnlyCollection<ResetCorrectionTypes> expectedResult = null;

        _resetCorrectionTypeServiceMock
            .Setup(s => s.GetAllAsync())
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAllAsync();

        // Assert
        result.Should().BeOfType<NotFoundResult>();
    }

}
