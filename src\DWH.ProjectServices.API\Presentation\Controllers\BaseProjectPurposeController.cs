﻿using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace DWH.ProjectServices.API.Presentation.Controllers;

public class BaseProjectPurposeController : ApiController
{
    private readonly IBaseProjectPurposeService _baseProjectPurposeService;

    public BaseProjectPurposeController(IBaseProjectPurposeService baseProjectPurposeService)
    {
        _baseProjectPurposeService = baseProjectPurposeService;
    }

    [HttpGet]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(IEnumerable<BaseProjectPurposeResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetAllAsync()
    {
        var baseProjectPurposess = await _baseProjectPurposeService.GetAllAsync();
        var result = baseProjectPurposess?.Select(p => new BaseProjectPurposeResponse
        {
            Id = p.Id,
            Name = p.Name
        }).ToList();

        return OkOrEmpty(result);
    }
}
