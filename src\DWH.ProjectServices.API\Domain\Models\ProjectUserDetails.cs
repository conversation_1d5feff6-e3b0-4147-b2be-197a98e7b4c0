﻿namespace DWH.ProjectServices.API.Domain.Models;

public class ProjectUserDetails
{
    public int ProjectId { get; set; }
    public List<UserDetails> Users { get; set; }

    public ProjectUserDetails()
    {
        Users = new List<UserDetails>();
    }
}

public class UserDetails
{
    public int Id { get; set; }
    public string AssignedBy { get; set; }
    public DateTime AssignedOn { get; set; }
    public string DeletedBy { get; set; }
    public DateTime? DeletedOn { get; set; }
}
