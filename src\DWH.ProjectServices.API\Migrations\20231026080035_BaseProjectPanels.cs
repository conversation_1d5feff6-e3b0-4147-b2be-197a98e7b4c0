﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DWH.ProjectServices.API.Migrations;

/// <inheritdoc />
public partial class BaseProjectPanels : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "BaseProject_Panels",
            columns: table => new
            {
                Id = table.Column<int>(type: "integer", nullable: false)
                    .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                Name = table.Column<string>(type: "character varying(40)", maxLength: 40, nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_BaseProject_Panels", x => x.Id);
            });
        migrationBuilder.InsertData(
           table: "BaseProject_Panels",
           columns: new[] { "Id", "Name" },
           values: new object[,]
           {
                { "1", "POS (Standard)" },
                { "2", "Total Store (TSR)" },
                { "3", "Distributor (IDAS DST)" }
           });
        migrationBuilder.CreateTable(
            name: "BaseProjects",
            columns: table => new
            {
                Id = table.Column<int>(type: "integer", nullable: false)
                    .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                Name = table.Column<string>(type: "character varying(40)", maxLength: 40, nullable: true),
                TypeId = table.Column<int>(type: "integer", nullable: false),
                PanelId = table.Column<int>(type: "integer", nullable: false),
                PeriodicityId = table.Column<int>(type: "integer", nullable: false),
                CountryId = table.Column<int>(type: "integer", nullable: false),
                IsRelevantForReportingEnabled = table.Column<bool>(type: "boolean", nullable: false),
                CreatedBy = table.Column<int>(type: "integer", nullable: false),
                CreatedWhen = table.Column<DateTime>(type: "timestamp with time zone", nullable: false, defaultValue: new DateTime(2023, 10, 26, 8, 0, 35, 779, DateTimeKind.Utc).AddTicks(8635)),
                UpdatedBy = table.Column<int>(type: "integer", nullable: true),
                UpdatedWhen = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                DeletedBy = table.Column<int>(type: "integer", nullable: true),
                DeletedWhen = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                Deleted = table.Column<bool>(type: "boolean", nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_BaseProjects", x => x.Id);
                table.ForeignKey(
                    name: "FK_BaseProject_Panels_BaseProjects_PanelId",
                    column: x => x.PanelId,
                    principalTable: "BaseProject_Panels",
                    principalColumn: "Id");
            });

        migrationBuilder.CreateTable(
            name: "BaseProject_Predecessors",
            columns: table => new
            {
                Id = table.Column<int>(type: "integer", nullable: false)
                    .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                BaseProjectId = table.Column<int>(type: "integer", nullable: false),
                PredecessorId = table.Column<int>(type: "integer", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_BaseProject_Predecessors", x => x.Id);
                table.ForeignKey(
                    name: "FK_BaseProject_Predecessors_BaseProjects_BaseProjectId",
                    column: x => x.BaseProjectId,
                    principalTable: "BaseProjects",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateTable(
            name: "BaseProject_ProductGroups",
            columns: table => new
            {
                Id = table.Column<int>(type: "integer", nullable: false)
                    .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                BaseProjectId = table.Column<int>(type: "integer", nullable: false),
                ProductGroupId = table.Column<int>(type: "integer", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_BaseProject_ProductGroups", x => x.Id);
                table.ForeignKey(
                    name: "FK_BaseProject_ProductGroups_BaseProjects_BaseProjectId",
                    column: x => x.BaseProjectId,
                    principalTable: "BaseProjects",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateIndex(
            name: "IX_BaseProject_Predecessors_BaseProjectId",
            table: "BaseProject_Predecessors",
            column: "BaseProjectId");

        migrationBuilder.CreateIndex(
            name: "IX_BaseProject_ProductGroups_BaseProjectId",
            table: "BaseProject_ProductGroups",
            column: "BaseProjectId");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "BaseProject_Panels");

        migrationBuilder.DropTable(
            name: "BaseProject_Predecessors");

        migrationBuilder.DropTable(
            name: "BaseProject_ProductGroups");

        migrationBuilder.DropTable(
            name: "BaseProjects");
    }
}
