﻿using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace DWH.ProjectServices.API.Presentation.Controllers;

public class BaseProjectPanelsController : ApiController
{
    private readonly IBaseProjectPanelService _baseProjectPanelService;

    public BaseProjectPanelsController(IBaseProjectPanelService baseProjectPanelService)
    {
        _baseProjectPanelService = baseProjectPanelService;
    }

    [HttpGet]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(IEnumerable<BaseProjectPanelResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status204NoContent)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetAllAsync()
    {
        var baseProjectPanels = await _baseProjectPanelService.GetAllAsync();
        var result = baseProjectPanels?.Select(p => new BaseProjectPanelResponse
        {
            Id = p.Id,
            Name = p.Name
        }).ToList();


        return OkOrEmptyList(result);
    }
}

