﻿using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations;

public class DomainProductGroupConfigurations : IEntityTypeConfiguration<DomainProductGroup>
{
    public void Configure(EntityTypeBuilder<DomainProductGroup> builder)
    {
        builder.ToSqlQuery(@"SELECT dpg.DOMAIN_PRODUCTGROUP_ID, dpg.DOMAIN_PRODUCTGROUP_DDESC, aps.SECTOR_SDESC
                                FROM DWH_META.ADM_PG_DOMAIN_PRODUCTGROUP dpg
                                INNER JOIN DWH_META.ADM_PG_PRODUCTGROUP pg
                                ON dpg.DOMAIN_PRODUCTGROUP_ID = pg.DOMAIN_PRODUCTGROUP_ID
                                INNER JOIN DWH_META.ADM_PG_Sector aps
                                ON dpg.SECTOR_ID = aps.SECTOR_ID")
        .<PERSON><PERSON><PERSON>(ps => new { ps.DomainProductGroupId, ps.DomainProductGroupDDesc, ps.SectorSDesc });
    }
}
