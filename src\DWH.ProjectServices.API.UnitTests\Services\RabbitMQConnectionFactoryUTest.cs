﻿using DWH.ProjectServices.API.Infrastructure.RabbitMQ;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Configuration;
using FluentAssertions;
using Microsoft.Extensions.Options;
using Moq;

namespace DWH.ProjectServices.API.UnitTests.Services;

public class RabbitMQConnectionFactoryTests
{
    [Fact]
    public void Constructor_NullSettings_ThrowsArgumentNullException()
    {
        // Arrange
        IOptions<RabbitMQSettings> rabbitMQSettings = null;

        // Act
        Func<object> act = () => new RabbitMQConnectionFactory(rabbitMQSettings);

        // Assert
        act.Should().Throw<ArgumentNullException>()
            .WithMessage("RabbitMQ settings must not be null (Parameter 'rabbitMQSettings')");
    }

    [Theory]
    [InlineData("", "guest", "guest", "/")]
    [InlineData("localhost", "", "guest", "/")]
    [InlineData("localhost", "guest", "", "/")]
    [InlineData("localhost", "guest", "guest", "")]
    public void Constructor_InvalidSettings_ThrowsArgumentException(string hostName, string userName, string password, string virtualHost)
    {
        // Arrange
        var rabbitMQSettings = new RabbitMQSettings
        {
            HostName = hostName,
            UserName = userName,
            Password = password,
            VirtualHost = virtualHost
        };

        var optionsMock = new Mock<IOptions<RabbitMQSettings>>();
        optionsMock.Setup(x => x.Value).Returns(rabbitMQSettings);

        // Act
        Action act = () => new RabbitMQConnectionFactory(optionsMock.Object);

        // Assert
        var exception = act.Should().Throw<ArgumentException>()
            .WithMessage($"The value cannot be an empty string. (Parameter '{GetParameterName(hostName, userName, password, virtualHost)}')");


        string GetParameterName(string host, string user, string pass, string vHost)
        {
            if (string.IsNullOrEmpty(host)) return "HostName";
            if (string.IsNullOrEmpty(user)) return "UserName";
            if (string.IsNullOrEmpty(pass)) return "Password";
            if (string.IsNullOrEmpty(vHost)) return "VirtualHost";
            return string.Empty;
        }

    }
}
