﻿using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Services.Helper.Interface;

namespace DWH.ProjectServices.API.Services.Helper;

public class RoleHelper : IRoleHelper
{
    private const string API_ENDPOINT = "/api/userroles/approveduserIds";
    private readonly IRoleApiClient _roleApiClient;

    public RoleHelper(IRoleApiClient roleApiClient)
    {
        _roleApiClient = roleApiClient;
    }
    public async Task<List<int>> GetMasterUsers()
    {
        var response = await _roleApiClient.GetAsync<List<int>>(API_ENDPOINT);

        if (response.IsSuccess)
        {
            return response.Data;
        }
        else
        {
            throw new HttpRequestException($"Error calling API for master users: Status Code {(int)response.StatusCode} ({response.ReasonPhrase})");
        }
    }
}
