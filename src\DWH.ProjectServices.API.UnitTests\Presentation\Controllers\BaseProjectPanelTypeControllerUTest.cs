﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Controllers;
using DWH.ProjectServices.API.Services.Interfaces;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Moq;

namespace DWH.ProjectServices.API.UnitTests.Presentation.Controllers;

public class BaseProjectDataTypeControllerTests
{
    private readonly Mock<IBaseProjectDataTypeService> _baseProjectDataTypeServiceMock;
    private readonly BaseProjectDataTypeController _controller;

    public BaseProjectDataTypeControllerTests()
    {
        _baseProjectDataTypeServiceMock = new Mock<IBaseProjectDataTypeService>();
        _controller = new BaseProjectDataTypeController(_baseProjectDataTypeServiceMock.Object);
    }

    [Fact]
    public async Task GetAllAsync_When_CalledForNotEmptyResult_Expect_200Response()
    {
        // Arrange
        var expectedResult = new List<DataTypes>
        {
            new DataTypes { Id = 1, Name = "DataType1", Description = "DataTypeDesc1" },
            new DataTypes { Id = 2, Name = "DataType2", Description = "DataTypeDesc2" }
        };

        _baseProjectDataTypeServiceMock
            .Setup(s => s.GetAllAsync())
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAllAsync();

        // Assert
        result.Should().BeOfType<OkObjectResult>()
            .Which.Value.Should().BeEquivalentTo(expectedResult);
    }

    [Fact]
    public async Task GetAllAsync_When_CalledForEmptyResult_Expect_404NotFound()
    {
        // Arrange
        List<DataTypes> expectedResult = null;

        _baseProjectDataTypeServiceMock
            .Setup(s => s.GetAllAsync())
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAllAsync();

        // Assert
        result.Should().BeOfType<NotFoundResult>();
    }

}
