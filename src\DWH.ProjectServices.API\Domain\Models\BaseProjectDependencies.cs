﻿using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;

namespace DWH.ProjectServices.API.Domain.Models;

public class BaseProjectDependencies
{
    public IReadOnlyList<RBProject> RBBPProjects { get; init; } = new List<RBProject>();
    public IReadOnlyList<ProductionProject> ProductionProjects { get; init; } = new List<ProductionProject>();
    public IReadOnlyList<ReportingProject> ReportingProjects { get; init; } = new List<ReportingProject>();
}
