﻿using DWH.ProjectServices.API.Domain.Models;
using FluentAssertions;

namespace DWH.ProjectServices.API.UnitTests.Domain.Models;

public class BaseProjectDeletesTests
{
    [Fact]
    public void Constructor_When_IdsIsEmpty_ShouldThrowArgumentException()
    {
        // Arrange
        var emptyIds = new List<int>();
        var deletedBy = "testUser";
        var userName = "testUser";

        // Act
        Action act = () => new BaseProjectDeletes(emptyIds, deletedBy, userName);

        // Assert
        var exception = act.Should().Throw<ArgumentException>()
            .WithMessage("Must have at least one id to proceed (Parameter 'ids')");
    }
}
