﻿using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Services.Interfaces;

namespace DWH.ProjectServices.API.Services;

public class QCProjectService : IQCProjectService
{
    private readonly IQCProjectRepository _qcProjectRepository;
    private readonly IOutBoxItemRepository _outBoxItemRepository;

    public QCProjectService(IQCProjectRepository qcProjectRepository, IOutBoxItemRepository outBoxItemRepository)
    {
        _qcProjectRepository = qcProjectRepository;
        _outBoxItemRepository = outBoxItemRepository;
    }
    public async Task<QCProject> UpdateAsync(int qcProjectId, QCProjectUpdates editQCProject)
    {
        var result = await _qcProjectRepository.UpdateAsync(qcProjectId, editQCProject);

        await _outBoxItemRepository.SaveMessagesAsync(
            new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = result.BaseProjectId },
            ProjectMessageType.QCProjectUpdate);
        return result;
    }

    public async Task<List<int>> GetAsync(QCProjectCountries qcProjectCountryRequest)
    {
        var result = await _qcProjectRepository.GetByQCProjectIdAsync(qcProjectCountryRequest);
        return result;
    }

    public async Task<int> GetFilteredQCProjectAsync(QCProjectCountryIds qcProjectCountryRequest)
    {
        var result = await _qcProjectRepository.GetFilteredQCProjectIdAsync(qcProjectCountryRequest);
        return result;
    }

}
