﻿using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations;

public class RBProjectDomainPGConfigurations : IEntityTypeConfiguration<RBProjectDomainPG>
{
    public void Configure(EntityTypeBuilder<RBProjectDomainPG> builder)
    {
        builder.ToTable(Constants.ADM_PRJ_RBPROJECT_DOMAIN_PG, Constants.DWH_META);
        builder.Property(p => p.Id).HasColumnName("DOMAIN_PRODUCTGROUP_ID");
        builder.Property(p => p.RbProjectId).HasColumnName("RBPROJECTID");
    }
}
