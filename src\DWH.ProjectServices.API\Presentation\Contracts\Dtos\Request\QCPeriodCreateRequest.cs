﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;

public class QCPeriodCreateRequest
{
    [Required]
    public int QCProjectId { get; set; }
    [Required]
    public long PeriodId { get; set; }
    [Required]
    public int? Status { get; set; }
    public ICollection<ReferencePeriodRequest> Periods { get; set; }
    public StockInitiliazationRequest StockInitialization { get; set; }

    [JsonIgnore]
    public string CreatedBy { get; set; }
    [JsonIgnore]
    public string UpdatedBy { get; set; }
}
