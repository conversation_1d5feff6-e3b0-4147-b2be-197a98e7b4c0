﻿using System.ComponentModel.DataAnnotations;
using DWH.ProjectServices.API.Domain.Models;
using FluentAssertions;

namespace DWH.ProjectServices.API.UnitTests.Domain.Models;

public class QcPeriodDeletesTests
{
    [Fact]
    public void QCPeriodDeleteDomainModel_When_IdsAreEmpty_ShouldReturnValidationResult()
    {
        // Arrange
        var qcPeriodDeleteDomainModel = new QCPeriodDeletes
        {
            Ids = new List<long>()
        };

        // Act
        var validationResults = qcPeriodDeleteDomainModel.Validate(new ValidationContext(qcPeriodDeleteDomainModel)).ToList();

        // Assert
        validationResults.Should().NotBeEmpty();
        validationResults.First().ErrorMessage.Should().Be("Must have atleast one id to proceed");
    }

}
