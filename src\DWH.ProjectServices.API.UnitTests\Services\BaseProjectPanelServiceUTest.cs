﻿using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Services;
using FluentAssertions;
using Moq;

namespace DWH.ProjectServices.API.UnitTests.Services;

public class BaseProjectPanelServiceTests
{
    private readonly Mock<IBaseProjectPanelRepository> _baseProjectPanelRepositoryStub;
    private readonly BaseProjectPanelService _service;

    public BaseProjectPanelServiceTests()
    {
        _baseProjectPanelRepositoryStub = new Mock<IBaseProjectPanelRepository>();
        _service = new BaseProjectPanelService(_baseProjectPanelRepositoryStub.Object);
    }

    [Fact]
    public async Task GetAllAsync_WhenCalled_WithData_ReturnsBaseProjectPanels()
    {
        // Arrange
        var baseProjectPanelEntities = new List<BaseProjectPanel>
        {
            new BaseProjectPanel { Id = 1, Name = "Panel1" },
            new BaseProjectPanel { Id = 2, Name = "Panel2" }
        };

        var expectedBaseProjectPanels = new List<BaseProjectPanel>
        {
            new BaseProjectPanel { Id = 1, Name = "Panel1" },
            new BaseProjectPanel { Id = 2, Name = "Panel2" }
        };

        _baseProjectPanelRepositoryStub
            .Setup(repo => repo.GetAllAsync())
            .ReturnsAsync(baseProjectPanelEntities);

        // Act
        var result = await _service.GetAllAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(expectedBaseProjectPanels);
    }

    [Fact]
    public async Task GetAllAsync_WhenCalled_WithNoData_ReturnsEmptyCollection()
    {
        // Arrange
        var emptyBaseProjectPanelEntities = new List<BaseProjectPanel>();

        _baseProjectPanelRepositoryStub
            .Setup(repo => repo.GetAllAsync())
            .ReturnsAsync(emptyBaseProjectPanelEntities);

        // Act
        var result = await _service.GetAllAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task GetAllAsync_WhenCalled_AndRepositoryThrowsException_ThrowsException()
    {
        // Arrange
        _baseProjectPanelRepositoryStub
            .Setup(repo => repo.GetAllAsync())
            .ThrowsAsync(new EntityNotExistsException($"BaseProject Panels Not Found"));

        // Act
        var act = async () => await _service.GetAllAsync();

        // Assert
        await act.Should().ThrowAsync<EntityNotExistsException>();
    }


}
