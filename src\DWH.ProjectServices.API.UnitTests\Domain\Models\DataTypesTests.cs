﻿using AutoFixture;
using DWH.ProjectServices.API.Domain.Models;
using FluentAssertions;

namespace DWH.ProjectServices.API.UnitTests.Domain.Models;

public class DataTypesTests
{
    private readonly Fixture _fixture;

    public DataTypesTests()
    {
        _fixture = new Fixture();
    }

    [Fact]
    public void IdProperty_CanBeSetAndRetrieved()
    {
        // Arrange
        var dataType = new DataTypes();
        var id = _fixture.Create<int>();

        // Act
        dataType.Id = id;

        // Assert
        dataType.Id.Should().Be(id);
    }

    [Fact]
    public void NameProperty_CanBeSetAndRetrieved()
    {
        // Arrange
        var dataType = new DataTypes();
        var name = _fixture.Create<string>();

        // Act
        dataType.Name = name;

        // Assert
        dataType.Name.Should().Be(name);
    }

    [Fact]
    public void DescProperty_CanBeSetAndRetrieved()
    {
        // Arrange
        var dataType = new DataTypes();
        var desc = _fixture.Create<string>();

        // Act
        dataType.Description = desc;

        // Assert
        dataType.Description.Should().Be(desc);
    }
}
