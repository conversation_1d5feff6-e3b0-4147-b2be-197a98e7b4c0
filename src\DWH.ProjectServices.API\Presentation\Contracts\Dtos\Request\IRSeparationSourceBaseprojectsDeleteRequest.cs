﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;

public class IRSeparationSourceBaseprojectsDeleteRequest
{
    [Required]
    public List<int> RetailerSeperationIds { get; set; }

    public string Message { get; set; }

    [JsonIgnore]
    public string Email { get; set; }

    public IEnumerable<ValidationResult> Validate()
    {
        var results = new List<ValidationResult>();
        if (RetailerSeperationIds.Count <= 0)
        {
            results.Add(new ValidationResult("Must have atleast one id to proceed"));
        }

        return results;
    }
}
