﻿using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations;

public class RBBaseProjectConfigurations : IEntityTypeConfiguration<RBBaseProject>
{
    public void Configure(EntityTypeBuilder<RBBaseProject> builder)
    {
        builder.ToTable(Constants.ADM_PRJ_RBPROJECT_BASEPROJECT, Constants.DWH_META);
        builder.Property(p => p.Id).HasColumnName("RBPROJECTID");
        builder.Property(p => p.BaseProjectId).HasColumnName("BASEPROJECTID");
        builder.Property(p => p.Deleted).HasColumnName("DELETED");
        builder.Property(p => p.UpdatedBy).HasColumnName("CHANGEDBY");
        builder.Property(p => p.<PERSON>).HasColumnName("CHANGEDWHEN");

    }
}
