﻿using System.Net;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using Polly;
using Polly.CircuitBreaker;
using Polly.Retry;

namespace DWH.ProjectServices.API.Infrastructure.WebServiceClient;

public class PollyPolicyHelper : IPollyPolicyHelper
{
    private readonly ILogger<PollyPolicyHelper> _logger;

    public PollyPolicyHelper(ILogger<PollyPolicyHelper> logger)
    {
        _logger = logger;
    }

    public AsyncRetryPolicy<ServiceResponse<T>> GetRetryPolicy<T>()
    {
        return Policy
            .HandleResult<ServiceResponse<T>>(r => r.StatusCode == HttpStatusCode.InternalServerError || r.StatusCode == HttpStatusCode.ServiceUnavailable)
            .Or<HttpRequestException>()
            .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                onRetry: (outcome, timespan, retryAttempt, context) =>
                {
                    _logger.LogWarning("Request failed with {ErrorMessage}. Waiting {Timespan} before next retry. Retry attempt {RetryAttempt}.",
                        outcome.Result?.ErrorMessage, timespan, retryAttempt);
                });
    }

    public AsyncCircuitBreakerPolicy<ServiceResponse<T>> GetCircuitBreakerPolicy<T>()
    {
        return Policy
            .HandleResult<ServiceResponse<T>>(r => r.StatusCode == HttpStatusCode.InternalServerError || r.StatusCode == HttpStatusCode.ServiceUnavailable)
            .Or<HttpRequestException>()
            .CircuitBreakerAsync(2, TimeSpan.FromMinutes(1),
                onBreak: (outcome, breakDelay) =>
                {
                    _logger.LogWarning("Circuit breaker opened due to {ErrorMessage}. Breaking for {BreakDelay}.",
                        outcome.Result?.ErrorMessage, breakDelay);
                },
                onReset: () => _logger.LogInformation("Circuit breaker reset."),
                onHalfOpen: () => _logger.LogInformation("Circuit breaker half-opened."));
    }
}
