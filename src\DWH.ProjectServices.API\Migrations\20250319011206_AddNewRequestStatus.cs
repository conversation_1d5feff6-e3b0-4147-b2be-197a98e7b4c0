﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DWH.ProjectServices.API.Migrations;

/// <inheritdoc />
public partial class AddNewRequestStatus : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.InsertData(
        table: "RequestStatus",
        columns: new[] { "Id", "Name" },
        values: new object[,]
        {
            { "6", "CSV Available" }
        });
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DeleteData(
            table: "RequestStatus",
            keyColumn: "Id",
            keyValues: new object[] { "6" });
    }
}
