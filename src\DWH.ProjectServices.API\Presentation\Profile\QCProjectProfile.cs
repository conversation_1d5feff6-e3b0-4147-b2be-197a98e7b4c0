﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;

namespace DWH.ProjectServices.API.Presentation.Profile;

public class QCProjectandPeriodProfile : AutoMapper.Profile
{
    public QCProjectandPeriodProfile()
    {
        CreateMap<QCProjectEditResponse, QCProject>().ReverseMap();
        CreateMap<QCPeriodCreateRequest, QCPeriod>().ReverseMap();
        CreateMap<ReferencePeriodRequest, Period>().ReverseMap();
        CreateMap<StockInitiliazationRequest, StockInitialization>().ReverseMap();
        CreateMap<QCPeriodResponse, QCPeriod>().ReverseMap();
        CreateMap<QCPeriodEditResponse, QCPeriod>().ReverseMap();
        CreateMap<QCPeriodModelDto, QCPeriod>()
                     .ForMember(dest => dest.CreatedWhen, opt => opt.MapFrom(src => src.DateOfCreation))
                     .ReverseMap();
        CreateMap<QCPeriodModelDto, QCPeriodEntity>()
.ForMember(dest => dest.CreatedWhen, opt => opt.MapFrom(src => src.DateOfCreation))
.ReverseMap();
        CreateMap<QCPeriodEntity, QCPeriodWithBPIdResponse>().ReverseMap();
        CreateMap<QCPeriodDeleteRequest, QCPeriodDeletes>().ReverseMap();
        CreateMap<QCProjectEditRequest, QCProjectUpdates>().ReverseMap();
        CreateMap<QCProjectCountryRequest, QCProjectCountries>().ReverseMap();
        CreateMap<QCProjectEditResponse, QCProject>().ReverseMap();
        CreateMap<QCPeriodEditRequest, QCPeriodEdits>().ReverseMap();
        CreateMap<QCPeriodEditResponse, QCPeriod>().ReverseMap();
        CreateMap<AutoQCPeriods, AutoQCPeriodCreateRequest>().ReverseMap();
        CreateMap<AutoQCResponses, AutoQCPeriodResponse>().ReverseMap();
        CreateMap<BulkQCPeriods, BulkQCPeriodRequest>().ReverseMap();
        CreateMap<BulkQCResponses, BulkQCPeriodResponse>().ReverseMap();

    }
}
