﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DWH.ProjectServices.API.Migrations;

/// <inheritdoc />
public partial class AddingOutBoxItems : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AlterColumn<DateTimeOffset>(
            name: "Created<PERSON><PERSON>",
            table: "QCPeriod",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(2025, 2, 11, 11, 24, 39, 637, DateTimeKind.Unspecified).AddTicks(8148), new TimeSpan(0, 0, 0, 0, 0)),
            oldClrType: typeof(DateTimeOffset),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTimeOffset(new DateTime(2025, 1, 7, 10, 54, 4, 757, DateTimeKind.Unspecified).AddTicks(4523), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AlterColumn<DateTimeOffset>(
            name: "<PERSON><PERSON><PERSON>",
            table: "BaseProject",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(2025, 2, 11, 11, 24, 39, 637, DateTimeKind.Unspecified).AddTicks(7445), new TimeSpan(0, 0, 0, 0, 0)),
            oldClrType: typeof(DateTimeOffset),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTimeOffset(new DateTime(2025, 1, 7, 10, 54, 4, 757, DateTimeKind.Unspecified).AddTicks(2370), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.CreateTable(
            name: "OutBoxItem",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uuid", nullable: false),
                Payload = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                Status = table.Column<string>(type: "text", nullable: false),
                CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                ProcessedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                SyncingEntityId = table.Column<int>(type: "integer", nullable: false),
                TypeId = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_OutBoxItem", x => x.Id);
            });
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "OutBoxItem");

        migrationBuilder.AlterColumn<DateTimeOffset>(
            name: "CreatedWhen",
            table: "QCPeriod",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(2025, 1, 7, 10, 54, 4, 757, DateTimeKind.Unspecified).AddTicks(4523), new TimeSpan(0, 0, 0, 0, 0)),
            oldClrType: typeof(DateTimeOffset),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTimeOffset(new DateTime(2025, 2, 11, 11, 24, 39, 637, DateTimeKind.Unspecified).AddTicks(8148), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AlterColumn<DateTimeOffset>(
            name: "CreatedWhen",
            table: "BaseProject",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(2025, 1, 7, 10, 54, 4, 757, DateTimeKind.Unspecified).AddTicks(2370), new TimeSpan(0, 0, 0, 0, 0)),
            oldClrType: typeof(DateTimeOffset),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTimeOffset(new DateTime(2025, 2, 11, 11, 24, 39, 637, DateTimeKind.Unspecified).AddTicks(7445), new TimeSpan(0, 0, 0, 0, 0)));
    }
}
