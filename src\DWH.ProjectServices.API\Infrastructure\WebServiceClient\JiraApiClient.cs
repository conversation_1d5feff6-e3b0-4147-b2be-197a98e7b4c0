﻿using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Constants;
using DWH.ProjectServices.API.Services.Helper.Interface;

namespace DWH.ProjectServices.API.Infrastructure.WebServiceClient;

public class JiraAPIClient : BaseApiClient, IJiraApiClient
{
    public JiraAPIClient(
        IHttpClientFactory httpClientFactory,
        ITokenService tokenService,
        ILogger<JiraAPIClient> logger,
        IPollyPolicyHelper pollyHelper)
        : base(httpClientFactory, tokenService, logger, pollyHelper)
    {
    }

    public async Task<ServiceResponse<T>> GetAsync<T>(string requestUri)
    {
        var client = await GetHttpClientAsync("JiraAPI", AppConstants.JiraAPI);
        return await ExecuteWithPoliciesAsync<T>(() => client.GetAsync(requestUri), requestUri);
    }

    public async Task<ServiceResponse<T>> PostAsync<T>(string requestUri, StringContent requestContent)
    {
        var client = await GetHttpClientAsync("JiraAPI", AppConstants.JiraAPI);
        return await ExecuteWithPoliciesAsync<T>(() => client.PostAsync(requestUri, requestContent), requestUri);
    }

    public async Task<ServiceResponse<T>> PutAsync<T>(string requestUri, StringContent requestContent)
    {
        var client = await GetHttpClientAsync("JiraAPI", AppConstants.JiraAPI);
        return await ExecuteWithPoliciesAsync<T>(() => client.PutAsync(requestUri, requestContent), requestUri);
    }
}
