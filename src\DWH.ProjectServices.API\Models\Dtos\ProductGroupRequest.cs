﻿using System.ComponentModel.DataAnnotations;

namespace DWH.ProjectServices.API.Models.Dtos;

public class ProductGroupRequest : IValidatableObject
{
    public int[] CountryIds { get; set; }
    public long[] SectorIds { get; set; }
    public long[] CategoryIds { get; set; }
    public int[] PanelIds { get; set; }
    public int[] DomainProductGroupIds { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();

        if (CategoryIds is not null && CountryIds.Any(i => i < 0))
        {
            results.Add(new ValidationResult("Invalid Country Found"));
        }

        if (CategoryIds is not null && SectorIds.Any(i => i < 0))
        {
            results.Add(new ValidationResult("Invalid Sector Found"));
        }

        if (CategoryIds is not null && CategoryIds.Any(i => i < 0))
        {
            results.Add(new ValidationResult("Invalid Category Found"));
        }

        if (PanelIds is not null && PanelIds.Any(i => i < 0))
        {
            results.Add(new ValidationResult("Invalid PanelId Found"));
        }
        if (DomainProductGroupIds is not null && DomainProductGroupIds.Any(i => i < 0))
        {
            results.Add(new ValidationResult("Invalid DomainProductGroupId Found"));
        }
        return results;
    }
}
