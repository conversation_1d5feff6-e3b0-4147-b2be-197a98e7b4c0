﻿using System.Collections.ObjectModel;
using System.Net;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using AutoFixture;
using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Configuration;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Constants;
using DWH.ProjectServices.API.IntegrationTests.MockService;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using RabbitMQ.Client;
using Xunit.Abstractions;

namespace DWH.ProjectServices.API.IntegrationTests.Presentation.Controllers;



public class QCPeriodControllerTest : IClassFixture<APIFactory>
{


    private readonly APIFactory _factory;
    private readonly IConnection _connection;
    private readonly IChannel _channel;
    private readonly IFixture _fixture;
    private readonly ITestOutputHelper _outputHelper;
    private const string PATH = "/api/v1/qcperiods/qcperiod";

    public QCPeriodControllerTest(APIFactory apiFactory, ITestOutputHelper outputHelper)
    {

        _factory = apiFactory;
        _fixture = new Fixture();
        _outputHelper = outputHelper;

        var rabbitMQSettings = Options.Create(new RabbitMQSettings
        {
            HostName = "localhost",
            UserName = "guest",
            Password = "guest",
            VirtualHost = "/"
        });
        var connectionFactory = new RabbitMQConnectionFactory(rabbitMQSettings);
        _connection = connectionFactory.CreateConnectionAsync().GetAwaiter().GetResult();
        _channel = _connection.CreateChannelAsync().GetAwaiter().GetResult();

    }

    [Fact]
    public async Task AddAsync_QCPeriod_SendToRabbitMQ_SuccessfullySendsMessage()
    {
        using (var scope = _factory.Services.CreateScope())
        {
            var client = _factory.CreateClient();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
            var specificQCProjectId = 1;
            _fixture.Customize<QCPeriodCreateRequest>(composer =>
                composer.With(qc => qc.QCProjectId, specificQCProjectId)
                .With(qc => qc.CreatedBy, "testUser")
                .With(qc => qc.UpdatedBy, "testUser")
            );
            var body = _fixture.Create<QCPeriodCreateRequest>();
            var initialQCPeriodCount = await dbContext.QCPeriod.CountAsync();

            // Add the userName to the request header
            client.DefaultRequestHeaders.Add("userName", "testUser");

            // ACT
            var result = await client.PostAsJsonAsync(PATH, body);
            result.Should().NotBeNull();
            result.StatusCode.Should().Be(HttpStatusCode.Created);

            // Get the ID from the result
            var responseContent = await result.Content.ReadFromJsonAsync<QCPeriodResponse>();
            responseContent.Should().NotBeNull();

            // Extract the ID from the response
            var createdId = responseContent?.Id;
            var lastMessage = GetLastMessageFromQueue(RMQConstants.ProjectServicesQueue);

            var lastMessageObject = JObject.Parse(lastMessage);
            var lastMessageId = (int?)lastMessageObject["SyncingEntityId"];
            var finalQCPeriodCount = await dbContext.QCPeriod.CountAsync();

            finalQCPeriodCount.Should().Be(initialQCPeriodCount + 1);
            // Assert
            lastMessage.Should().NotBeNull();
            lastMessageId.Should().Be(createdId);
        }
    }

    [Fact]
    public async Task UpdateAsync_QCPeriod_SendToRabbitMQ_SuccessfullySendsMessage()
    {
        using (var scope = _factory.Services.CreateScope())
        {
            var client = _factory.CreateClient();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
            var qcperiodId = 14;
            string PATH = $"/api/v1/qcperiods/qcperiods/{qcperiodId}";

            // Set the userName for the request header
            client.DefaultRequestHeaders.Add("userName", "testUser");

            _fixture.Customize<QCPeriodEditRequest>(qcp => qcp.With(x => x.Periods, new Collection<RefPeriodsEditRequest>
    {
        new RefPeriodsEditRequest
        {
            index = 1,
            RefProjectId = 10,
            RefPeriodId = 10,
        }
    }));

            var qcPeriodEditRequest = _fixture.Create<QCPeriodEditRequest>();

            // Act
            var result = await client.PutAsJsonAsync(PATH, qcPeriodEditRequest);

            // Assert
            result.StatusCode.Should().Be(HttpStatusCode.OK);
            result.Should().NotBeNull();

            // Get the ID from the result
            var responseContent = await result.Content.ReadFromJsonAsync<QCPeriodEditResponse>();
            responseContent.Should().NotBeNull();

            // Extract the ID from the response

            var SyncId = "1-165";
            var lastMessage = GetLastMessageFromQueue(RMQConstants.ProjectServicesQueue);

            var lastMessageObject = JObject.Parse(lastMessage);
            var lastMessageId = (string?)lastMessageObject["SyncingEntityId"];

            // Assert
            lastMessage.Should().NotBeNull();
            lastMessageId.Should().Be(SyncId);
        }
    }

    [Fact]
    public async Task DeleteAsync_QCPeriod_SendToRabbitMQ_SuccessfullySendsMessage()
    {
        using (var scope = _factory.Services.CreateScope())
        {
            var client = _factory.CreateClient();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
            var httpClient = _factory.CreateClient();
            var SyncId = "1-117";
            List<long> qcPeriodIds = new List<long> { 4 };
            _fixture.Customize<QCPeriodDeleteRequest>(q => q.With(x => x.Ids, qcPeriodIds));
            var body = _fixture.Create<QCPeriodDeleteRequest>();
            var initialQCPeriodCount = await dbContext.QCPeriod.CountAsync();
            var stringContent = new StringContent(JsonSerializer.Serialize(body), Encoding.UTF8, "application/json");
            var request = new HttpRequestMessage(HttpMethod.Delete, $"/api/v1/qcperiods/qcperiods");

            request.Content = stringContent;

            var response = await httpClient.SendAsync(request);

            response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
            var lastMessage = GetLastMessageFromQueue(RMQConstants.ProjectServicesQueue);

            var lastMessageObject = JObject.Parse(lastMessage);
            var syncingEntityIdArray = (JArray?)lastMessageObject["SyncingEntityId"];
            var lastMessageId = syncingEntityIdArray?[0].ToString();
            var finalQCPeriodCount = await dbContext.QCPeriod.CountAsync();


            // Assert

            lastMessage.Should().NotBeNull();
            lastMessageId.Should().Be(SyncId);
            finalQCPeriodCount.Should().Be(initialQCPeriodCount - 1);
        }
    }

    [Fact]
    public async Task CreateAsyncBulkQc_ReturnsMultiStatusAndCreatesBulkQCPeriod()
    {

        // Arrange
        using (var scope = _factory.Services.CreateScope())
        {
            var client = _factory.CreateClient();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
            var initialQCPeriodCount = await dbContext.QCPeriod.CountAsync();
            client.DefaultRequestHeaders.Add("userName", "testUser");
            // Create a bulk QC period request
            var bulkQCPeriodRequest = new BulkQCPeriodRequest
            {
                StartPeriod = 20240299999030,
                EndPeriod = 20240699999030,

            };

            // ACT
            var result = await client.PostAsJsonAsync($"/api/v1/qcperiods/qcperiod/bulkQCPeriod/{5}", bulkQCPeriodRequest);
            // ASSERT

            result.Should().NotBeNull();
            result.StatusCode.Should().Be(HttpStatusCode.MultiStatus);


        }
    }


    [Fact]

    public async Task CreateAsyncAutoQc_ReturnsMultiStatusAndCreatesBulkQCPeriod()
    {

        using (var scope = _factory.Services.CreateScope())
        {
            var client = _factory.CreateClient();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();


            var autoQCPeriodCreateRequest = new AutoQCPeriodCreateRequest
            {
                QCProjectIds = new List<int> { 5 }
            };
            var targetPeriodId = 20240699999030;

            _fixture.Customize<AutoQCPeriodCreateRequest>(composer =>
                composer.With(aq => aq.QCProjectIds, autoQCPeriodCreateRequest.QCProjectIds)
            );
            var body = _fixture.Create<AutoQCPeriodCreateRequest>();
            var initialQCPeriodCount = await dbContext.QCPeriod.CountAsync();

            // Add the userName to the request header
            client.DefaultRequestHeaders.Add("userName", "testUser");
            string PATH = $"/api/v1/qcperiods/{targetPeriodId}/extend";

            // ACT
            var result = await client.PostAsJsonAsync(PATH, body);
            result.Should().NotBeNull();
            result.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
            var responseContent = await result.Content.ReadFromJsonAsync<AutoQCPeriodResponse>();
            responseContent.Should().NotBeNull();



        }
    }

    [Fact]
    public async Task DeleteAsyncQCPeriod_Returns_BadRequest_When_Last_QCPeriod_Is_Deleted()
    {
        using (var scope = _factory.Services.CreateScope())
        {
            var client = _factory.CreateClient();
            var dbContext = scope.ServiceProvider.GetRequiredService<OracleDbContext>();
            var qcStatusPeriodandBP = await dbContext.QCStatus.Where(x => x.Deleted == 0 && x.Status == 6).Select(i => new { i.RepPeriodId, i.QCProjectId }).FirstOrDefaultAsync();
            var postgresdbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
            if (qcStatusPeriodandBP != null)
            {
                var qcperiodId = await postgresdbContext.QCPeriod
                    .Where(x => qcStatusPeriodandBP.QCProjectId == x.QCProjectId && qcStatusPeriodandBP.RepPeriodId == x.PeriodId)
                    .Select(i => i.Id)
                    .FirstOrDefaultAsync();
                var deleteRequest = new QCPeriodDeleteRequest
                {
                    Ids = new List<long> { qcperiodId }
                };

                var stringContent = new StringContent(JsonSerializer.Serialize(deleteRequest), Encoding.UTF8, "application/json");
                var request = new HttpRequestMessage(HttpMethod.Delete, $"/api/v1/qcperiods/qcperiods");
                request.Content = stringContent;
                client.DefaultRequestHeaders.Add("userName", "testUser");
                var response = await client.SendAsync(request);

                // Assert: Validate response
                response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
                var responseContent = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions(JsonSerializerDefaults.Web)
                {
                    IncludeFields = true
                };
                var result = JsonSerializer.Deserialize<IReadOnlyList<ResponseInfoQCPeriod>>(responseContent, options);

                result.Should().NotBeNull();
                result.Should().ContainSingle();

                var responseInfo = result?.First();
                responseInfo?.StatusCode.Should().Be((int)HttpStatusCode.BadRequest);
                responseInfo?.StatusMsg.Should().Contain($"QC Period(s) cannot be deleted as they are currently in QC Status");
            }
        }
    }

    [Fact]
    public async Task AddAsync_QCPeriod_Should_CreateOutboxItem()
    {
        using var scope = _factory.Services.CreateScope();
        var serviceProvider = scope.ServiceProvider;
        var dbContext = serviceProvider.GetRequiredService<PostgreSqlDbContext>();
        var client = _factory.CreateClient();

        client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
        client.DefaultRequestHeaders.Add("Custom-Countryid", "47"); // Ensure valid country ID

        var initialQCPeriodCount = await dbContext.QCPeriod.CountAsync();

        var qcPeriodCreateRequest = new QCPeriodCreateRequest
        {
            QCProjectId = 1, // Use valid existing QCProjectId
            PeriodId = 20240809999008, // Ensure new unique period ID
            Status = 1, // Required Status

            Periods = new List<ReferencePeriodRequest>
        {
        new ReferencePeriodRequest
        {
            RefProjectId = 1, // Assume valid project reference
            RefPeriodId = 20240399999030 // Reference existing period
        }
        },

            StockInitialization = new StockInitiliazationRequest
            {
                StockBaseProjectId = null, // Allows nulls
                StockPeriodId = null
            }
        };

        var content = new StringContent(JsonSerializer.Serialize(qcPeriodCreateRequest), Encoding.UTF8, "application/json");

        var response = await client.PostAsync("/api/v1/qcperiods/qcperiod", content);

        if (!response.IsSuccessStatusCode)
        {
            var errorContent = await response.Content.ReadAsStringAsync();
            _outputHelper.WriteLine($"❌ Response Status Code: {response.StatusCode}");
            _outputHelper.WriteLine($"❌ Response Content: {errorContent}");
        }

        response.StatusCode.Should().Be(HttpStatusCode.Created);
        response.Should().NotBeNull();

        var finalQCPeriodCount = await dbContext.QCPeriod.CountAsync();
        finalQCPeriodCount.Should().Be(initialQCPeriodCount + 1);

        var responseContent = await response.Content.ReadFromJsonAsync<QCPeriodResponse>();
        responseContent.Should().NotBeNull();
        responseContent?.QCProjectId.Should().Be(qcPeriodCreateRequest.QCProjectId);
        responseContent?.PeriodId.Should().Be(qcPeriodCreateRequest.PeriodId);

        var outboxItem = await dbContext.OutBoxItem
            .OrderByDescending(oi => oi.CreatedAt)
            .FirstOrDefaultAsync(oi => oi.TypeId == ProjectMessageType.QCPeriodCreate.ToString());

        outboxItem.Should().NotBeNull();
        outboxItem?.TypeId.Should().Be(ProjectMessageType.QCPeriodCreate.ToString());
        outboxItem?.Status.Should().Be(OutboxStatus.Pending); // Expected status
    }

    [Fact]
    public async Task UpdateAsync_QCPeriod_Should_UpdateOutboxItem()
    {
        using var scope = _factory.Services.CreateScope();
        var serviceProvider = scope.ServiceProvider;
        var dbContext = serviceProvider.GetRequiredService<PostgreSqlDbContext>();
        var client = _factory.CreateClient();

        // ✅ Set Required Headers
        client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
        client.DefaultRequestHeaders.Add("Custom-Countryid", "47"); // Ensure valid country ID

        // ✅ Use an Existing QCPeriod ID from the Test Database
        var existingQCPeriod = await dbContext.QCPeriod.OrderBy(x => x.Id).FirstOrDefaultAsync();
        existingQCPeriod.Should().NotBeNull(); // Ensure we have a valid QCPeriod to update
        if (existingQCPeriod != null)
        {
            var qcPeriodId = existingQCPeriod.Id; // Use valid QCPeriodId from DB

            // ✅ Prepare Request Payload with Required Fields
            var qcPeriodEditRequest = new QCPeriodEditRequest
            {
                PeriodId = existingQCPeriod.PeriodId, // Use the existing PeriodId
                Periods = new List<RefPeriodsEditRequest>
    {
        new RefPeriodsEditRequest
        {
            RefProjectId = 1, // Assume valid project reference
            RefPeriodId = 20240399999030 // Reference existing period
        }
    },
                StockInitialization = new StockInitiliazationRequest
                {
                    StockBaseProjectId = 0, // Allows nulls
                    StockPeriodId = 0
                }
            };

            var content = new StringContent(JsonSerializer.Serialize(qcPeriodEditRequest), Encoding.UTF8, "application/json");

            // ✅ Act - Send API Request
            var response = await client.PutAsync($"/api/v1/qcperiods/qcperiods/{qcPeriodId}", content);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _outputHelper.WriteLine($"❌ Response Status Code: {response.StatusCode}");
                _outputHelper.WriteLine($"❌ Response Content: {errorContent}");
            }

            // ✅ Assert Response
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            response.Should().NotBeNull();

            // ✅ Fetch Updated QCPeriod Response
            var responseContent = await response.Content.ReadFromJsonAsync<QCPeriodEditResponse>();
            responseContent.Should().NotBeNull();
            responseContent?.PeriodId.Should().Be(qcPeriodEditRequest.PeriodId);

            // ✅ Fetch latest OutBoxItem
            var outboxItem = await dbContext.OutBoxItem
                .OrderByDescending(oi => oi.CreatedAt)
                .FirstOrDefaultAsync(oi => oi.TypeId == ProjectMessageType.QCPeriodUpdate.ToString());

            // ✅ Assert OutBoxItem entry is created
            outboxItem.Should().NotBeNull();
            outboxItem?.TypeId.Should().Be(ProjectMessageType.QCPeriodUpdate.ToString());
            outboxItem?.Status.Should().Be(OutboxStatus.Pending); // Expected status
        }
    }

    [Fact]
    public async Task DeleteAsync_QCPeriod_Should_RemoveEntryFromDBAndCreateOutboxItem()
    {
        using var scope = _factory.Services.CreateScope();
        var serviceProvider = scope.ServiceProvider;
        var dbContext = serviceProvider.GetRequiredService<PostgreSqlDbContext>();
        var client = _factory.CreateClient();

        // ✅ Set Required Headers
        client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
        client.DefaultRequestHeaders.Add("Custom-Countryid", "47"); // Ensure valid country ID

        // ✅ Fetch an existing QCPeriod from DB
        var existingQCPeriod = await dbContext.QCPeriod.OrderBy(x => x.Id).FirstOrDefaultAsync();
        existingQCPeriod.Should().NotBeNull(); // Ensure valid QCPeriod exists
        if (existingQCPeriod != null)
        {
            var qcPeriodId = existingQCPeriod.Id; // Use valid QCPeriodId from DB

            // ✅ Get Initial OutBoxItem Count Before Delete
            var initialOutboxCount = await dbContext.OutBoxItem.CountAsync();

            // ✅ Prepare Request Payload
            var qcPeriodDeleteRequest = new QCPeriodDeleteRequest
            {
                Ids = new List<long> { qcPeriodId }
            };

            var content = new StringContent(JsonSerializer.Serialize(qcPeriodDeleteRequest), Encoding.UTF8, "application/json");

            // ✅ Act - Send API Request
            var response = await client.SendAsync(new HttpRequestMessage
            {
                Method = HttpMethod.Delete,
                RequestUri = new Uri("/api/v1/qcperiods/qcperiods", UriKind.Relative),
                Content = content
            });

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _outputHelper.WriteLine($"❌ Response Status Code: {response.StatusCode}");
                _outputHelper.WriteLine($"❌ Response Content: {errorContent}");
            }

            // ✅ Assert Response
            response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
            response.Should().NotBeNull();

            // ✅ Ensure DB Change Takes Effect
            await dbContext.SaveChangesAsync();
            dbContext.ChangeTracker.Clear(); // Clear EF Core cache to force fresh query

            // ✅ Verify Database: QCPeriod is Deleted
            var deletedQCPeriod = await dbContext.QCPeriod.FindAsync(qcPeriodId);
            deletedQCPeriod.Should().BeNull(); // Ensure QCPeriod was removed

            // ✅ Fetch latest OutBoxItem
            var finalOutboxCount = await dbContext.OutBoxItem.CountAsync();
            var outboxItem = await dbContext.OutBoxItem
                .OrderByDescending(oi => oi.CreatedAt)
                .FirstOrDefaultAsync(oi => oi.TypeId == ProjectMessageType.QCPeriodDelete.ToString());

            // ✅ Assert OutBoxItem entry is created
            outboxItem.Should().NotBeNull();
            outboxItem?.TypeId.Should().Be(ProjectMessageType.QCPeriodDelete.ToString());
            outboxItem?.Status.Should().Be(OutboxStatus.Pending); // Expected status before processing

            // ✅ Ensure OutBoxItem count has increased by 1
            finalOutboxCount.Should().Be(initialOutboxCount + 1);
        }
    }






    private string GetLastMessageFromQueue(string queueName)
    {
        _channel.QueueDeclareAsync(queue: queueName,
                              durable: true,
                              exclusive: false,
                              autoDelete: false,
                              arguments: null);

        BasicGetResult? result = null;
        string lastMessage = string.Empty;

        while ((result = _channel.BasicGetAsync(queueName, true).Result) != null)
        {
            var body = result.Body.ToArray();
            lastMessage = Encoding.UTF8.GetString(body);
        }

        return string.IsNullOrEmpty(lastMessage) ? string.Empty : lastMessage;
    }
}
