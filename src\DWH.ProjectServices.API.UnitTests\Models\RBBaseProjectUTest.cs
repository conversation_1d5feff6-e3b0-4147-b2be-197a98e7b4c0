﻿using AutoFixture;
using DWH.ProjectServices.API.Models;
using FluentAssertions;

namespace DWH.ProjectServices.API.UnitTests.Models;

public class RBBaseProjectTests
{
    private readonly IFixture _fixture;

    public RBBaseProjectTests()
    {
        _fixture = new Fixture();
    }

    [Fact]
    public void When_ValidParameters_Expect_NotNullInstance()
    {
        // Arrange
        var id = _fixture.Create<int>();
        var baseProjectId = _fixture.Create<int>();
        var deleted = _fixture.Create<int>();

        // Act
        var instance = new RBBaseProject
        {
            Id = id,
            BaseProjectId = baseProjectId,
            Deleted = deleted
        };

        // Assert
        instance.Should().NotBeNull();
    }
}
