﻿using AutoFixture;
using DWH.ProjectServices.API.Models;
using FluentAssertions;

namespace DWH.ProjectServices.API.UnitTests.Models;

public class ProjectSubTypeTests
{
    private readonly IFixture _fixture;

    public ProjectSubTypeTests()
    {
        _fixture = new Fixture();
    }

    [Fact]
    public void When_ValidParameters_Expect_NotNullInstance()
    {
        // Arrange
        var subProjectId = _fixture.Create<int>();
        var subProjectName = _fixture.Create<string>();

        // Act
        var instance = new ProjectSubType(subProjectId, subProjectName);

        // Assert
        instance.Should().NotBeNull();
    }
}
