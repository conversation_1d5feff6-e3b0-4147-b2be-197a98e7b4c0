﻿using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using FluentAssertions;

namespace DWH.ProjectServices.API.UnitTests.Models;

public class BaseProjectProductGroupTests
{
    [Fact]
    public void When_ValidParameters_Expect_NotNullInstance()
    {
        // Arrange
        var instance = new BaseProjectProductGroupEntity
        {
            Id = 1,
            BaseProjectId = 2,
            ProductGroupId = 3,
            Deleted = false
        };

        // Act & Assert

        instance.Should().NotBeNull();
    }
}
