{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"Oracle": {"UserId": "ACCESS_LM_API", "Password": "#{ConnectionStrings:Oracle:Password}", "DataSource": "#{ConnectionStrings:Oracle:DataSource}"}, "PostgreSQL": {"DefaultConnection": "Server=localhost;Port=5432;Database=project_services_dbTest;UserId=postgres;Password=", "Password": ""}, "RabbitMQ": {"HostName": "", "UserName": "", "Password": "", "VirtualHost": ""}}, "RetryLimit": {"Value": 3, "Delay": 10}, "OutboxCleanup": {"RetentionDays": 14}, "Cors": {"PolicyName": "ProjectServicesPolicy", "Origins": "http://localhost:4200", "ExposedHeaders": "traceparent", "Enable": false}, "CustomException": {"TraceDetails": false}, "Health": {"Pattern": "/health"}, "Prometheus": {"Enable": "true", "UseHttpMetrics": "true", "Pattern": "/metrics"}, "Swagger": {"Enable": true, "ArtifactId": "PROJECTSERVICES_API", "AppName": "ProjectServices.API", "Title": "Project Services", "Version": "v1", "EnableXMLPath": true, "EnableBearerAuth": true, "DevelopmentContractPath": "contracts", "XmlFileName": "DWH.ProjectServices.API.xml"}, "AzureAdTestClient": {"Url": "https://login.microsoftonline.com", "ClientId": "#{AzureAdTestClient:ClientId}", "ClientSecret": "#{AzureAdTestClient:ClientSecret}", "Scope": "https://GfkDataPlatform.onmicrosoft.com/dp.dwh.bx.api/.default"}, "AzureAdB2C": {"Instance": "https://gfkdataplatform.b2clogin.com", "ClientId": "#{AzureAdB2C:ClientId}", "TenantId": "#{AzureAdB2C:TenantId}", "Domain": "GfkDataPlatform.onmicrosoft.com", "SignUpSignInPolicyId": "B2C_1A_SUSI_INTERNAL", "AdditionalSignUpSignInPolicyId": "B2C_1_signin_all"}, "Tracing": {"ServiceName": "ProjectServices.API", "ServiceNamespace": "DEV", "Developer": "DWH", "ConsoleTracing": "false"}, "WebServiceClient": {"BaseAddress": {"DateAPI": "https://date-api-shared.t1.dwh.in.gfk.com/", "AdministratorAPI": "https://administrator-api.t1.dwh.in.gfk.com/", "JiraAPI": "https://loadmonitor-api.t1.dwh.in.gfk.com/", "Origin": "https://builder-ui.t1.dwh.in.gfk.com/", "ProjectServicesSecurityAPI": "https://projectservicessecurity-api.t1.dwh.in.gfk.com/", "RoleAPI": "https://userroleassignment-api.t1.dwh.in.gfk.com/", "ProjectServicesAPI": "https://projectservices-api.t1.dwh.in.gfk.com"}}, "ExchangeEntitySettings": {"RetailerSeparationExchangeEnabled": true}, "TokenSettings": {"ClientId": "#{TokenSettings:ClientId}", "GrantType": "client_credentials", "Scope": "https://GfkDataPlatform.onmicrosoft.com/dwh.shared.date/.default", "ClientSecret": "#{TokenSettings:ClientSecret}", "Endpoint": "https://GfkDataPlatform.b2clogin.com/tfp/GfkDataPlatform.onmicrosoft.com/B2C_1A_SUSI_INTERNAL/oauth2/v2.0/token"}}