﻿using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations;

public class ProductGroupConfigurations : IEntityTypeConfiguration<ProductGroup>
{
    public void Configure(EntityTypeBuilder<ProductGroup> builder)
    {
        builder.ToTable(Constants.ADM_PG_PRODUCTGROUP, Constants.DWH_META);
        builder.Property(p => p.Id).HasColumnName("PRODUCTGROUP_ID");
        builder.Property(p => p.Description).HasColumnName("PRODUCTGROUP_DESC");
        builder.Property(p => p.CountryId).HasColumnName("COUNTRY_ID");
        builder.Property(p => p.SectorId).HasColumnName("SECTOR_ID");
        builder.Property(p => p.CategoryId).HasColumnName("CATEGORY_ID");
        builder.Property(p => p.PanelId).HasColumnName("DOM_PGR_TYPE_ID");
        builder.Property(p => p.HybridFlag).HasColumnName("HYBRID_FLAG");
        builder.Property(p => p.Deleted).HasColumnName("DELETED");
        builder.Property(p => p.DomainProductGroupId).HasColumnName("DOMAIN_PRODUCTGROUP_ID");

    }
}
