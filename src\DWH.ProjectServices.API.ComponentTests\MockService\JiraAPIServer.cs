﻿using WireMock.Server;

namespace DWH.ProjectServices.API.IntegrationTests.MockService;

public class <PERSON>raAPIServer
{
    public WireMockServer Server { get; }

    public JiraAPIServer()
    {
        Server = WireMockServer.Start();
        ConfigureWireMockStubs(Server);
    }

    private void ConfigureWireMockStubs(WireMockServer server)
    {
        server
        .Given(WireMock.RequestBuilders.Request.Create()
            .WithPath("/api/v1/retailerseperation")
            .UsingPost()
            .WithHeader("Content-Type", "application/json")
            .WithHeader("userName", "testUser")
            .WithBodyAsJson(new
            {
                fromPeriodId = 1,
                toPeriodId = 2,
                resetCorrection = true,
                extrapolation = true,
                retailerSeperations = new[]
                {
                    new
                    {
                        sourceBPId = 123,
                        requestStatusId = 1
                    }
                }
            }))
        .RespondWith(WireMock.ResponseBuilders.Response.Create()
            .WithStatusCode(201) // 201 Created
            .WithBodyAsJson(new
            {
                Id = 456,
                FromPeriodId = 1,
                ToPeriodId = 2,
                ResetCorrection = true,
                Extrapolation = true,
                RequestedBy = "testUser",
                RequestedWhen = DateTimeOffset.UtcNow,
                JiraId = "JIRA-123",
                RetailerSeperations = new[]
                {
                    new
                    {
                        sourceBPId = 123,
                        requestStatusId = 1
                    }
                }
            }));

        server
        .Given(WireMock.RequestBuilders.Request.Create()
            .WithPath("/api/v1/retailerseperation")
            .UsingPost()
            .WithHeader("Content-Type", "application/json")
            .WithHeader("userName", "testUser")
            .WithBodyAsJson(new
            {
                fromPeriodId = 1,
                toPeriodId = 2,
                resetCorrection = true,
                extrapolation = true,
                retailerSeperations = new[]
                {
                    new
                    {
                        sourceBPId = 123,
                        requestStatusId = 4
                    }
                }
            }))
        .RespondWith(WireMock.ResponseBuilders.Response.Create()
            .WithStatusCode(500) // 500 Internal Server Error
            .WithBodyAsJson(new
            {
                error = "Internal Server Error",
                message = "An unexpected error occurred while processing the request."
            }));

    }

    public void Dispose()
    {
        Server.Stop();
        Server.Dispose();
    }
}
