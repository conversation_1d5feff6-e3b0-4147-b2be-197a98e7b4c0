﻿using System.Text.Json.Serialization;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;

namespace DWH.ProjectServices.API.Domain.Models;

public class QCPeriodEdits
{
    public long PeriodId { get; set; }
    public ICollection<RefPeriodsEditRequest> Periods { get; set; }

    public StockInitiliazationRequest StockInitialization { get; set; }

    [JsonIgnore]
    public string UpdatedBy { get; set; }
}
