﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DWH.ProjectServices.API.Migrations;

/// <inheritdoc />
public partial class RetailerSeperationDataModel : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {

        migrationBuilder.CreateTable(
            name: "RequestStatus",
            columns: table => new
            {
                Id = table.Column<int>(type: "integer", nullable: false)
                    .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_RequestStatus", x => x.Id);
            });

        migrationBuilder.InsertData(
        table: "RequestStatus",
        columns: new[] { "Id", "Name" },
        values: new object[,]
        {
                { "1", "Pending" },
                { "2", "Executing" },
                { "3", "Finished" },
                { "4", "Edited" },
                { "5", "Declined" }
        });

        migrationBuilder.CreateTable(
            name: "RetailerSeperationRequests",
            columns: table => new
            {
                Id = table.Column<int>(type: "integer", nullable: false)
                    .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                FromPeriodId = table.Column<long>(type: "bigint", nullable: false),
                ToPeriodId = table.Column<long>(type: "bigint", nullable: false),
                ResetCorrection = table.Column<bool>(type: "boolean", nullable: false),
                Extrapolation = table.Column<bool>(type: "boolean", nullable: false),
                JiraId = table.Column<string>(type: "text", nullable: true),
                RequestStatusId = table.Column<int>(type: "integer", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_RetailerSeperationRequests", x => x.Id);
                table.ForeignKey(
                  name: "FK_RequestStatus_RetailerSeperationRequests",
                  column: x => x.RequestStatusId,
                  principalTable: "RequestStatus",
                  principalColumn: "Id");
            });

        migrationBuilder.CreateTable(
            name: "RetailerSeperation",
            columns: table => new
            {
                Id = table.Column<int>(type: "integer", nullable: false)
                    .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                SourceBPId = table.Column<int>(type: "integer", nullable: false),
                RetailerBPId = table.Column<int>(type: "integer", nullable: false),
                RetailerSeperationRequestId = table.Column<int>(type: "integer", nullable: false),
                IsError = table.Column<bool>(type: "boolean", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_RetailerSeperation", x => x.Id);
                table.ForeignKey(
                    name: "FK_RetailerSeperation_RetailerSeperationRequests_RetailerSeper~",
                    column: x => x.RetailerSeperationRequestId,
                    principalTable: "RetailerSeperationRequests",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateTable(
            name: "RetailerSeperationRequestDetail",
            columns: table => new
            {
                Id = table.Column<int>(type: "integer", nullable: false)
                    .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                RetailerSeperationRequestId = table.Column<int>(type: "integer", nullable: false),
                UpdatedBy = table.Column<string>(type: "text", nullable: true),
                UpdatedWhen = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                RequestStatusId = table.Column<int>(type: "integer", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_RetailerSeperationRequestDetail", x => x.Id);
                table.ForeignKey(
                    name: "FK_RetailerSeperationRequestDetail_RetailerSeperationRequests_~",
                    column: x => x.RetailerSeperationRequestId,
                    principalTable: "RetailerSeperationRequests",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
                table.ForeignKey(
                    name: "FK_RetailerSeperationRequestDetail_RequestStatus",
                    column: x => x.RequestStatusId,
                    principalTable: "RequestStatus",
                    principalColumn: "Id");
            });

        migrationBuilder.CreateIndex(
          name: "IX_RetailerSeperation_RetailerSeperationRequestId",
          table: "RetailerSeperation",
          column: "RetailerSeperationRequestId");

        migrationBuilder.CreateIndex(
            name: "IX_RetailerSeperationRequestDetail_RetailerSeperationRequestId",
            table: "RetailerSeperationRequestDetail",
            column: "RetailerSeperationRequestId");

        migrationBuilder.CreateIndex(
           name: "IX_RequestStatus_RetailerSeperationRequestId",
           table: "RetailerSeperationRequests",
           column: "RequestStatusId");

        migrationBuilder.CreateIndex(
           name: "IX_RequestStatus_RetailerSeperationRequestDetailId",
           table: "RetailerSeperationRequestDetail",
           column: "RequestStatusId");
    }


    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
           name: "RequestStatus");

        migrationBuilder.DropTable(
            name: "RetailerSeperation");

        migrationBuilder.DropTable(
            name: "RetailerSeperationRequestDetail");

        migrationBuilder.DropTable(
            name: "RetailerSeperationRequests");
    }
}
