﻿using AutoFixture;
using DWH.ProjectServices.API.Models;
using FluentAssertions;

namespace DWH.ProjectServices.API.UnitTests.Models;

public class ReportingProjectTests
{
    private readonly IFixture _fixture;

    public ReportingProjectTests()
    {
        _fixture = new Fixture();
    }

    [Fact]
    public void When_ValidParameters_Expect_NotNullInstance()
    {
        // Arrange
        var id = _fixture.Create<int>();
        var baseProjectId = _fixture.Create<int>();
        var deleted = _fixture.Create<int>();
        var createdBy = _fixture.Create<string>();
        var createdWhen = _fixture.Create<string>();
        var changedBy = _fixture.Create<string>();
        var changedWhen = _fixture.Create<DateTime>();

        // Act
        var instance = new ReportingBaseProject
        {
            Id = id,
            BaseProjectId = baseProjectId,
            Deleted = deleted,
            CreatedBy = createdBy,
            CreatedWhen = createdWhen,
            ChangedBy = changedBy,
            ChangedWhen = changedWhen
        };

        // Assert
        instance.Should().NotBeNull();
        instance.Id.Should().Be(id);
        instance.BaseProjectId.Should().Be(baseProjectId);
        instance.Deleted.Should().Be(deleted);
        instance.CreatedBy.Should().Be(createdBy);
        instance.CreatedWhen.Should().Be(createdWhen);
        instance.ChangedBy.Should().Be(changedBy);
        instance.ChangedWhen.Should().Be(changedWhen);
    }
}
