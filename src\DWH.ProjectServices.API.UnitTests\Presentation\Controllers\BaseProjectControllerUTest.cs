﻿using System.ComponentModel.DataAnnotations;
using System.Net;
using AutoFixture;
using AutoMapper;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Configuration;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Presentation.Controllers;
using DWH.ProjectServices.API.Services.Interfaces;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Moq;
using Xunit.Extensions.AssertExtensions;

namespace DWH.ProjectServices.API.UnitTests.Presentation.Controllers;

public class BaseProjectControllerTests
{
    private readonly IFixture _fixture;
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<IBaseProjectService> _baseProjectServiceMock;
    private readonly BaseProjectsController _controller;

    public BaseProjectControllerTests()
    {
        _fixture = new Fixture();
        var mockResponse = new Mock<HttpResponse>();
        var mockHttpContext = new Mock<HttpContext>();
        _baseProjectServiceMock = new Mock<IBaseProjectService>();
        _mapperMock = new Mock<IMapper>();
        var rabbitMQSenderMock = new Mock<IRabbitMQSender>();
        var optionsMock = new Mock<IOptions<RabbitMQSettings>>();

        _controller = new BaseProjectsController(_mapperMock.Object, _baseProjectServiceMock.Object);

        mockHttpContext.Setup(h => h.Response).Returns(mockResponse.Object);
    }

    [Fact]
    public async Task AddAsync_When_SuccessfullyAdded_Expect_201Response()
    {
        // Arrange
        var username = _fixture.Create<string>();
        var newBaseProjectDto = _fixture.Create<BaseProjectCreateRequest>();
        var baseProjectResponse = _fixture.Build<BaseProject>()
                                          .With(x => x.Id, 123)
                                          .Create();
        var baseProjectResponseDto = _fixture.Build<BaseProjectResponse>()
                                             .With(x => x.Id, 123)
                                             .Create();

        var mockHttpContext = new Mock<HttpContext>();
        var mockResponse = new Mock<HttpResponse>();

        _controller.ControllerContext = new ControllerContext { HttpContext = mockHttpContext.Object };
        mockResponse.SetupGet(r => r.Headers).Returns(new HeaderDictionary());
        mockHttpContext.SetupGet(c => c.Response).Returns(mockResponse.Object);

        _mapperMock
            .Setup(m => m.Map<BaseProject>(newBaseProjectDto))
            .Returns(baseProjectResponse);

        _baseProjectServiceMock
            .Setup(s => s.AddAsync(baseProjectResponse))
            .ReturnsAsync(baseProjectResponse);

        _mapperMock
            .Setup(m => m.Map<BaseProjectResponse>(baseProjectResponse))
            .Returns(baseProjectResponseDto);

        // Act
        var result = await _controller.AddAsync(username, newBaseProjectDto) as ObjectResult;

        // Assert
        result.ShouldNotBeNull();
        result.StatusCode.Should().Be(StatusCodes.Status201Created);
    }

    [Fact]
    public async Task AddAsync_When_Unsuccessful_Returns_BadRequest()
    {
        // Arrange
        var username = _fixture.Create<string>();
        var newBaseProjectDto = _fixture.Create<BaseProjectCreateRequest>();
        var mockHttpContext = new Mock<HttpContext>();
        var mockResponse = new Mock<HttpResponse>();

        _controller.ControllerContext = new ControllerContext { HttpContext = mockHttpContext.Object };
        mockResponse.SetupGet(r => r.Headers).Returns(new HeaderDictionary());
        mockHttpContext.SetupGet(c => c.Response).Returns(mockResponse.Object);

        _baseProjectServiceMock
            .Setup(s => s.AddAsync(It.IsAny<BaseProject>()))
            .ReturnsAsync((BaseProject)null);

        // Act
        var result = await _controller.AddAsync(username, newBaseProjectDto) as ObjectResult;

        // Assert
        result.Should().NotBeNull();
        result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
    }


    [Fact]
    public async Task UpdateAsync_When_SuccessfullyAdded_Expect_200Response()
    {
        // Arrange
        var username = _fixture.Create<string>();
        var baseProjectId = 1;
        var baseProjectEditRequest = _fixture.Create<BaseProjectEditRequest>();

        var baseProjectEditDomain = _fixture.Build<BaseProject>()
                                            .With(x => x.Id, baseProjectId)
                                            .Create();

        var baseProjectEditResponse = _fixture.Create<BaseProjectEditResponse>();

        _mapperMock
            .Setup(m => m.Map<BaseProject>(baseProjectEditRequest))
            .Returns(baseProjectEditDomain);

        _baseProjectServiceMock
            .Setup(s => s.UpdateAsync(baseProjectId, baseProjectEditDomain))
            .ReturnsAsync(baseProjectEditDomain);

        _mapperMock
            .Setup(m => m.Map<BaseProjectEditResponse>(baseProjectEditDomain))
            .Returns(baseProjectEditResponse);

        // Act
        var result = await _controller.UpdateAsync(username, baseProjectId, baseProjectEditRequest);

        // Assert
        result.Should().BeOfType<OkObjectResult>()
            .Which.StatusCode.Should().Be(StatusCodes.Status200OK);

        var okResult = result as OkObjectResult;
        okResult.Should().NotBeNull();
        okResult!.Value.Should().BeOfType<BaseProjectEditResponse>();
        okResult.Value.Should().BeEquivalentTo(baseProjectEditResponse);
    }

    [Fact]
    public void BaseProjectEditRequest_When_NameIsEmpty_ShouldReturnValidationResult()
    {
        // Arrange
        var baseProjectEditRequest = new BaseProjectEditRequest
        {
            Name = string.Empty
        };

        // Act
        var validationResults = baseProjectEditRequest.Validate(new ValidationContext(baseProjectEditRequest)).ToList();

        // Assert
        validationResults.ShouldNotBeEmpty();
        validationResults.First().ErrorMessage.ShouldEqual("Base Project Name cannot be null or empty");
    }

    [Fact]
    public async Task GetAsync_When_CalledForNotEmptyResult_Expect_200Response()
    {
        // Arrange
        var baseProjectPredecessorRequest = _fixture.Create<BaseProjectPredecessorRequest>();
        var baseProjectPredecessorResponses = _fixture.CreateMany<BaseProjectPredecessorResponse>(3).ToList();
        var baseProjectDomainModel = _fixture.CreateMany<BaseProject>(3).ToList();

        _baseProjectServiceMock
            .Setup(repo => repo.GetAllAsync(It.IsAny<BaseProjectPredecessor>()))
            .ReturnsAsync(baseProjectDomainModel);

        _mapperMock
        .Setup(m => m.Map<List<BaseProjectPredecessorResponse>>(It.IsAny<List<BaseProject>>()))
        .Returns(baseProjectPredecessorResponses);

        _mapperMock
 .Setup(m => m.Map<ICollection<BaseProjectPredecessorResponse>>(baseProjectDomainModel))
 .Returns(baseProjectPredecessorResponses);

        // Act
        var result = await _controller.GetAsync(baseProjectPredecessorRequest);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult.Should().NotBeNull();
        okResult!.Value.Should().BeEquivalentTo(baseProjectPredecessorResponses);
    }

    [Fact]
    public async Task GetAsync_When_InvalidCountry_Expect_BadRequest()
    {
        //Arrange
        var baseProjectPredecessorRequest = _fixture.Create<BaseProjectPredecessorRequest>();
        baseProjectPredecessorRequest.CountryId = -1;

        // Act
        var result = await _controller.GetAsync(baseProjectPredecessorRequest);

        // Assert
        var objectResult = result as ObjectResult;
        objectResult.Should().NotBeNull();
        objectResult.StatusCode.Should().Be(400);
    }

    [Fact]
    public async Task GetAsyncList_When_CountryIdsInHeaderandRequestNotMatch()
    {
        // Arrange
        var baseProjectRequestListDto = new BaseProjectListRequest
        {
            CountryIds = new int[] { 4 }
        };
        var filteredCountryIds = new int[] { };

        var baseProjectResponseList = filteredCountryIds.Select(id =>
            _fixture.Build<BaseProjectListPayloadResponse>()
                    .With(x => x.CountryId, id)
                    .With(b => b.Id, 123)
                    .Create())
        .ToList();


        var baseProjectResponseListDto = new BaseProjectLists
        {
            Counts = 2,  // Example count
            MoreRecordsAvailable = false,
            Records = baseProjectResponseList // Your list of BaseProjectResponses
        };

        _baseProjectServiceMock
            .Setup(s => s.GetAsyncList(It.IsAny<BaseProjectsLists>()))
            .ReturnsAsync(baseProjectResponseListDto);

        _mapperMock
   .Setup(m => m.Map<BaseProjectsLists>(It.IsAny<BaseProjectListRequest>()))
   .Returns(new BaseProjectsLists { CountryIds = filteredCountryIds });

        _mapperMock
            .Setup(m => m.Map<BaseProjectListResponse>(It.IsAny<BaseProjectLists>()))
            .Returns(new BaseProjectListResponse
            {
                Records = baseProjectResponseList
            });


        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext()
        };
        _controller.ControllerContext.HttpContext.Items["FilteredBaseProjectListRequest"] = new BaseProjectListRequest
        {
            CountryIds = filteredCountryIds
        };

        // Act
        var result = await _controller.GetAsyncList(baseProjectRequestListDto) as ObjectResult;

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        var response = okResult.Value as BaseProjectListResponse;
        response.Should().NotBeNull();
        response.Records.Count.Should().Be(filteredCountryIds.Length);
    }



    [Fact]
    public async Task DeleteAsync_When_Called_With_ValidBaseProjectIds_ShouldNot_ThrowException()
    {
        //Arrange
        var username = _fixture.Create<string>();
        var baseProjectDeleteDto = _fixture.Create<BaseProjectDeleteRequest>();

        _baseProjectServiceMock
            .Setup(s => s.DeleteAsync(It.IsAny<BaseProjectDeletes>()))
            .ReturnsAsync(new List<Dependencies>());

        // Act
        var exception = await Record.ExceptionAsync(() => _controller.DeleteAsync(username, baseProjectDeleteDto));

        //Assert
        exception.ShouldBeNull();
    }

    [Fact]
    public void BaseProjectDeleteRequest_When_IdsAreEmpty_ShouldReturnValidationResult()
    {
        // Arrange
        var baseProjectDeleteRequest = new BaseProjectDeleteRequest
        {
            Ids = new List<int>()
        };

        // Act
        var validationResults = baseProjectDeleteRequest.Validate(new ValidationContext(baseProjectDeleteRequest)).ToList();

        // Assert
        validationResults.ShouldNotBeEmpty();
        validationResults.First().ErrorMessage.ShouldEqual("Must have atleast one id to proceed");
    }


    [Fact]
    public async Task DeleteAsync_When_Called_With_ValidBaseProjectIds_Should_Return_207result()
    {
        //Arrange
        var username = _fixture.Create<string>();
        var baseProjectDeleteDto = _fixture.Create<BaseProjectDeleteRequest>();
        _baseProjectServiceMock.Setup(s => s.DeleteAsync(It.IsAny<BaseProjectDeletes>()))
            .ReturnsAsync(new List<Dependencies>());

        // Act
        var result = await _controller.DeleteAsync(username, baseProjectDeleteDto);

        // Assert
        result.Should().BeOfType<ObjectResult>().Which.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);

    }

    [Fact]
    public async Task GetBaseProjectAsync_When_authorizedUser_Expect_OKResult()
    {
        // Arrange
        var baseProjectId = 123;
        var countryId = 1;
        var authorizedBaseProject = _fixture.Build<BaseProject>()
            .With(x => x.Id, baseProjectId)
            .With(x => x.CountryId, countryId)
            .With(x => x.Deleted, false)
            .Create();

        var baseProjectGetResponse = _fixture.Build<BaseProjectGetResponse>()
 .With(x => x.Id, baseProjectId)
 .With(x => x.CountryId, countryId)
 .With(x => x.Deleted, false)
 .Create();

        _mapperMock
            .Setup(m => m.Map<BaseProjectGetResponse>(It.IsAny<BaseProject>()))
            .Returns(baseProjectGetResponse);

        _baseProjectServiceMock.Setup(s => s.GetAsync(baseProjectId)).ReturnsAsync(authorizedBaseProject);

        //_mapperMock
        //    .Setup(m => m.Map<BaseProjectResponse>(It.IsAny<BaseProject>()))
        //    .Returns(baseProjectResponse);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext()
        };
        _controller.ControllerContext.HttpContext.Items["CountryIds"] = new string[] { countryId.ToString() };

        // Act
        var result = await _controller.GetBaseProjectAsync(baseProjectId);

        // Assert
        var objectResult = result as ObjectResult;
        objectResult.Should().NotBeNull();
        objectResult.StatusCode.Should().Be(200);
        objectResult.Value.Should().BeEquivalentTo(baseProjectGetResponse);
    }


    //[Fact]
    //public async Task GetBaseProjectAsync_When_authorizedUser_Expect_OKResult()
    //{
    //    // Arrange
    //    var countryIds = "1";
    //    var baseProjectId = 123;
    //    var deleted = false;
    //    _fixture.Customize<BaseProject>(b => b.With(x => x.CountryId, 1).With(b=>b.Id,123).With(y => y.Deleted, false));

    //    var authorizedBaseProject = _fixture.Create<BaseProject>(); // Assuming baseProject.CountryId is not in countryIds
    //    _baseProjectServiceMock.Setup(s => s.GetAsync(baseProjectId)).ReturnsAsync(authorizedBaseProject);


    //    _controller.ControllerContext = new ControllerContext
    //    {
    //        HttpContext = new DefaultHttpContext()
    //    };
    //    _controller.ControllerContext.HttpContext.Items["CountryId"] = new string[] { "1" };

    //    // Act
    //    var result = await _controller.GetBaseProjectAsync(baseProjectId);
    //    //Assert
    //    var objectResult = result as ObjectResult;
    //    objectResult.Should().NotBeNull(); // Ensure it's not null
    //    objectResult.StatusCode.Should().Be(200);
    //}

    [Fact]
    public async Task GetAsync_When_ServiceReturnsEmptyList_Expect_404NotFoundResponse()
    {
        // Arrange
        var request = new BaseProjectPredecessorRequest { CountryId = 1, PanelId = 1 };
        List<BaseProject> emptyResult = null;

        _baseProjectServiceMock
            .Setup(s => s.GetAllAsync(It.IsAny<BaseProjectPredecessor>()))
            .ReturnsAsync(emptyResult);

        // Act
        var result = await _controller.GetAsync(request);

        // Assert
        result.Should().BeOfType<NotFoundResult>();
    }

    [Fact]
    public async Task GetAsyncList_When_ServiceReturnsNull_Expect_404NotFound()
    {
        // Arrange
        var request = new BaseProjectListRequest();
        _baseProjectServiceMock
            .Setup(s => s.GetAsyncList(It.IsAny<BaseProjectsLists>()))
            .ReturnsAsync((BaseProjectLists)null);

        // Act
        var result = await _controller.GetAsyncList(request);

        // Assert
        result.Should().BeOfType<NotFoundResult>();
    }

    [Fact]
    public async Task GetBaseProjectAsync_When_ServiceReturnsNull_Expect_404NotFound()
    {
        // Arrange
        var baseProjectId = 1;
        BaseProject baseProject = null;

        _baseProjectServiceMock
            .Setup(s => s.GetAsync(baseProjectId))
            .ReturnsAsync(baseProject);


        // Act
        var result = await _controller.GetBaseProjectAsync(baseProjectId);

        // Assert
        result.Should().BeOfType<NotFoundResult>();
    }

    [Fact]
    public async Task GetAsyncListBaseProjectsByNameandId_When_ServiceReturnsData_Expect_200Ok()
    {
        // Arrange
        var countryid = "15";
        var request = new BaseProjectNameAndIdListRequest
        {
            CountryIds = new[] { 1, 2 }
        };

        var serviceResponse = new ProjectLists
        {
            Counts = 1,
            MoreRecordsAvailable = false,
            Records = new List<BaseProjectNameandIdResponse>
    {
        new BaseProjectNameandIdResponse { Id = 1, Name = "Test Project 1" },
        new BaseProjectNameandIdResponse { Id = 2, Name = "Test Project 2" }
    }
        };

        var mappedResponse = new BaseProjectNameandIdListResponse
        {
            Counts = 1,
            MoreRecordsAvailable = false,
            Records = new List<BaseProjectNameandIdResponse>
    {
        new BaseProjectNameandIdResponse { Id = 1, Name = "Test Project 1" },
        new BaseProjectNameandIdResponse { Id = 2, Name = "Test Project 2" }
    }
        };

        _baseProjectServiceMock
            .Setup(s => s.GetAsyncListBaseProjects(It.IsAny<BaseProjectNameandIdLists>()))
            .ReturnsAsync(serviceResponse);

        _mapperMock
            .Setup(m => m.Map<BaseProjectNameandIdListResponse>(serviceResponse))
            .Returns(mappedResponse); // ✅ This line fixes the issue

        // Act
        var result = await _controller.GetAsyncListBaseProjects(countryid, 0);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult!.Value.Should().BeEquivalentTo(mappedResponse);
    }


    [Fact]
    public async Task GetAsyncListBaseProjectsByNameandId_When_ServiceReturnsNull_Expect_404NotFound()
    {
        // Arrange
        var countryid = "15";
        var typeid = 3;
        _baseProjectServiceMock
            .Setup(s => s.GetAsyncListBaseProjects(It.IsAny<BaseProjectNameandIdLists>()))
            .ReturnsAsync((ProjectLists)null);

        // Act
        var result = await _controller.GetAsyncListBaseProjects(countryid, typeid);

        // Assert
        result.Should().BeOfType<NotFoundResult>();
    }

    [Fact]
    public async Task DeleteAsync_WhenQCStatusExists_ReturnsMultiStatusWithCorrectResponsesIncludingDependencies()
    {
        // Arrange
        var userName = "testUser";
        var baseProjectDeleteRequest = _fixture.Create<BaseProjectDeleteRequest>();
        var baseProjectDeletes = _fixture.Create<BaseProjectDeletes>();
        var baseProjectIds = baseProjectDeletes.Ids;

        var dependencies = new List<Dependencies>
            {
                new Dependencies(baseProjectIds.First().ToString(), baseProjectIds.First().ToString(),
                    (int)HttpStatusCode.BadRequest,
                    "This Base Project has at least one QC Period in QC Status", null, null),
                new Dependencies(baseProjectIds.Last().ToString(), baseProjectIds.Last().ToString(),
                    (int)HttpStatusCode.OK, HttpStatusCode.OK.ToString(), null, null)
            };

        var dependencyResponses = dependencies.Select(d => new DependencyResponse(
            d.BaseProjectId,
            d.QCProjectId,
            d.StatusCode,
            d.StatusMsg,
            null)).ToList();

        _baseProjectServiceMock
            .Setup(service => service.DeleteAsync(It.IsAny<BaseProjectDeletes>()))
            .ReturnsAsync(dependencies);

        _mapperMock
        .Setup(m => m.Map<IReadOnlyList<DependencyResponse>>(It.IsAny<IReadOnlyList<Dependencies>>()))
        .Returns((IReadOnlyList<Dependencies> deps) =>
        deps.Select(d => new DependencyResponse(
        d.BaseProjectId,
        d.QCProjectId,
        d.StatusCode,
        d.StatusMsg,
        d.Dependency == null ? null : new ProjectDependencies { QCStatusPeriodId = d.Dependency.QCStatusPeriodId }
    )).ToList());
        // Act
        var result = await _controller.DeleteAsync(userName, baseProjectDeleteRequest);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeType<ObjectResult>();

        var objectResult = result as ObjectResult;
        objectResult!.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);

        var actualResponses = objectResult.Value as IReadOnlyList<DependencyResponse>;
        actualResponses.Should().NotBeNull();
        actualResponses!.Count.Should().Be(2);

        actualResponses.Should().Contain(response =>
            response.BaseProjectId == baseProjectIds.First().ToString() &&
            response.StatusCode == (int)HttpStatusCode.BadRequest &&
            response.StatusMsg == "This Base Project has at least one QC Period in QC Status");

        actualResponses.Should().Contain(response =>
            response.BaseProjectId == baseProjectIds.Last().ToString() &&
            response.StatusCode == (int)HttpStatusCode.OK);
    }

    [Fact]
    public async Task GetBaseProjectAsync_When_ServiceReturnsValid_Expect_200Response()
    {
        // Arrange
        List<int> expectedResult = new List<int> { 1, 2, 3 };
        var baseProjectCountryRequest = _fixture.Create<BaseProjectCountryRequest>();

        _baseProjectServiceMock
            .Setup(s => s.GetAsync(It.IsAny<BaseProjectCountries>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetBaseProjectAsync(baseProjectCountryRequest);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult.Value.Should().BeOfType<List<int>>();
    }

    [Fact]
    public async Task AddBulkBpAsync_When_SuccessfullyAdded_Expect_207Response()
    {
        // Arrange
        var userName = _fixture.Create<string>();
        var countryIds = "1,2,3";
        var bulkBPCreateRequest = _fixture.Create<BulkBPCreateRequest>();
        var dependencies = _fixture.CreateMany<Dependencies>(3).ToList();
        var dependencyResponses = _fixture.CreateMany<DependencyResponse>(3).ToList();
        var bulkAddResponses = _fixture.CreateMany<BulkAddAsyncResponse>(3).ToList();

        _baseProjectServiceMock
            .Setup(s => s.AddBulkAsync(bulkBPCreateRequest.BaseProjectIds, userName, countryIds))
            .ReturnsAsync(dependencies);

        _mapperMock
            .Setup(m => m.Map<IReadOnlyList<BulkAddAsyncResponse>>(dependencies))
            .Returns(bulkAddResponses);

        // Act
        var result = await _controller.AddBulkBpAsync(userName, countryIds, bulkBPCreateRequest);

        // Assert
        result.Should().BeOfType<ObjectResult>()
            .Which.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
        var objectResult = result as ObjectResult;
        objectResult.Value.Should().BeEquivalentTo(bulkAddResponses);
    }

    [Fact]
    public async Task AddBulkBpAsync_When_ServiceReturnsNull_Expect_207Response()
    {
        // Arrange
        var userName = _fixture.Create<string>();
        var countryIds = "1,2,3";
        var bulkBPCreateRequest = _fixture.Create<BulkBPCreateRequest>();
        IReadOnlyList<Dependencies> dependencies = null;
        IReadOnlyList<BulkAddAsyncResponse> bulkAddResponses = null;


        _baseProjectServiceMock
            .Setup(s => s.AddBulkAsync(bulkBPCreateRequest.BaseProjectIds, userName, countryIds))
            .ReturnsAsync(dependencies);

        _mapperMock
            .Setup(m => m.Map<IReadOnlyList<BulkAddAsyncResponse>>(dependencies))
            .Returns(bulkAddResponses);

        // Act
        var result = await _controller.AddBulkBpAsync(userName, countryIds, bulkBPCreateRequest);

        // Assert
        result.Should().BeOfType<ObjectResult>()
            .Which.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
        var objectResult = result as ObjectResult;
        objectResult.Value.Should().BeNull();
    }

    [Fact]
    public async Task AddBulkBpAsync_When_ServiceReturnsEmptyList_Expect_207Response()
    {
        // Arrange
        var userName = _fixture.Create<string>();
        var countryIds = "1,2,3";
        var bulkBPCreateRequest = _fixture.Create<BulkBPCreateRequest>();
        var dependencies = new List<Dependencies>();
        var dependencyResponses = new List<DependencyResponse>();
        var bulkAddResponses = new List<BulkAddAsyncResponse>();


        _baseProjectServiceMock
            .Setup(s => s.AddBulkAsync(bulkBPCreateRequest.BaseProjectIds, userName, countryIds))
            .ReturnsAsync(dependencies);

        _mapperMock
            .Setup(m => m.Map<IReadOnlyList<BulkAddAsyncResponse>>(dependencies))
            .Returns(bulkAddResponses);

        // Act
        var result = await _controller.AddBulkBpAsync(userName, countryIds, bulkBPCreateRequest);

        // Assert
        result.Should().BeOfType<ObjectResult>()
            .Which.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
        var objectResult = result as ObjectResult;
        objectResult.Value.Should().BeEquivalentTo(bulkAddResponses);
    }

    [Fact]
    public async Task AddBulkBpAsync_When_CountryIdsIsNull_Expect_207Response()
    {
        // Arrange
        var userName = _fixture.Create<string>();
        string countryIds = null;
        var bulkBPCreateRequest = _fixture.Create<BulkBPCreateRequest>();
        var dependencies = _fixture.CreateMany<Dependencies>(2).ToList();
        var dependencyResponses = _fixture.CreateMany<DependencyResponse>(2).ToList();
        var bulkAddResponses = _fixture.CreateMany<BulkAddAsyncResponse>(2).ToList();


        _baseProjectServiceMock
            .Setup(s => s.AddBulkAsync(bulkBPCreateRequest.BaseProjectIds, userName, countryIds))
            .ReturnsAsync(dependencies);

        _mapperMock
            .Setup(m => m.Map<IReadOnlyList<BulkAddAsyncResponse>>(dependencies))
            .Returns(bulkAddResponses);

        // Act
        var result = await _controller.AddBulkBpAsync(userName, countryIds, bulkBPCreateRequest);

        // Assert
        result.Should().BeOfType<ObjectResult>()
            .Which.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
        var objectResult = result as ObjectResult;
        objectResult.Value.Should().BeEquivalentTo(bulkAddResponses);
    }

    [Fact]
    public async Task AddBulkBpAsync_When_ServiceThrowsException_Expect_ExceptionPropagated()
    {
        // Arrange
        var userName = _fixture.Create<string>();
        var countryIds = "1,2,3";
        var bulkBPCreateRequest = _fixture.Create<BulkBPCreateRequest>();
        var expectedException = new InvalidOperationException("Service error");

        _baseProjectServiceMock
            .Setup(s => s.AddBulkAsync(bulkBPCreateRequest.BaseProjectIds, userName, countryIds))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _controller.AddBulkBpAsync(userName, countryIds, bulkBPCreateRequest));

        exception.Should().Be(expectedException);
    }

    [Fact]
    public async Task GetBaseProjectAsync_When_ServiceReturnsEmptyList_Expect_403Forbidden()
    {
        // Arrange
        List<int> expectedResult = new List<int>();
        var baseProjectCountryRequest = _fixture.Create<BaseProjectCountryRequest>();

        var baseProjectCountryModel = _mapperMock.Object.Map<BaseProjectCountries>(baseProjectCountryRequest);

        _baseProjectServiceMock
            .Setup(s => s.GetAsync(It.IsAny<BaseProjectCountries>()))
            .ReturnsAsync(expectedResult); // Returning an empty list

        // Act
        var result = await _controller.GetBaseProjectAsync(baseProjectCountryRequest);

        // Assert
        result.Should().BeOfType<StatusCodeResult>()
            .Which.StatusCode.Should().Be(StatusCodes.Status403Forbidden);
    }

    [Fact]
    public async Task GetUsersList_When_ServiceReturnsEmptyList_ShouldReturnOkWithEmptyList()
    {
        // Arrange
        var baseProjectServiceMock = new Mock<IBaseProjectService>();
        baseProjectServiceMock
            .Setup(service => service.GetUsersList())
            .ReturnsAsync((IReadOnlyList<string>)null);

        // Act
        var result = await _controller.GetUsersList();
        result.Should().BeOfType<NotFoundResult>();
    }

    [Fact]
    public async Task GetUsersList_When_ServiceReturnsUsers_ShouldReturnOkWithUsers()
    {
        // Arrange
        var users = new List<string> { "alice", "bob" };
        _baseProjectServiceMock
            .Setup(service => service.GetUsersList())
            .ReturnsAsync(users);

        // Act
        var result = await _controller.GetUsersList();
        // Assert
        result.Should().BeOfType<OkObjectResult>()
            .Which.Value.Should().BeEquivalentTo(users);
    }
    [Fact]
    public async Task GetBaseProjectAssociationsAsync_WhenCalled_ReturnsOkWithDependencies()
    {
        // Arrange
        var baseProjectId = _fixture.Create<int>();
        var expectedDependencies = _fixture.Create<BaseProjectDependencies>();

        _baseProjectServiceMock
            .Setup(service => service.FetchBaseProjectAssociations(baseProjectId))
            .ReturnsAsync(expectedDependencies);

        // Act
        var result = await _controller.GetBaseProjectAssociationsAsync(baseProjectId);

        // Assert
        var okResult = result as OkObjectResult;
        okResult.Should().NotBeNull();
        okResult!.StatusCode.Should().Be(StatusCodes.Status200OK);
        okResult.Value.Should().BeEquivalentTo(expectedDependencies);
    }


    [Fact]
    public async Task UpdateBulkBpAsync_WithMoreThan100Ids_ReturnsBadRequest()
    {
        // Arrange
        var request = new BulkBPEditRequest
        {
            BaseProjectIds = Enumerable.Range(1, 101).ToList()
        };

        // Act
        var result = await _controller.UpdateBulkBpAsync("testUser", request);

        // Assert
        var badRequest = result.Should().BeOfType<BadRequestObjectResult>().Subject;
        badRequest.Value.Should().Be("Found more than 100 baseproject ids in the request");
    }

    [Fact]
    public async Task UpdateBulkBpAsync_WithValidRequest_ReturnsOk()
    {
        // Arrange
        var request = new BulkBPEditRequest
        {
            BaseProjectIds = Enumerable.Range(1, 10).ToList()
        };
        var mappedEntity = new BaseProjectBulkEdit();
        var serviceResult = new List<BaseProject>();

        var sampleResponse = _fixture.Create<BaseProjectEditResponse>();
        var response = new List<BaseProjectEditResponse> { sampleResponse };

        _mapperMock.Setup(m => m.Map<BaseProjectBulkEdit>(request)).Returns(mappedEntity);
        _baseProjectServiceMock.Setup(s => s.UpdateBulkAsync(mappedEntity)).ReturnsAsync(serviceResult);
        _mapperMock.Setup(m => m.Map<IReadOnlyList<BaseProjectEditResponse>>(serviceResult)).Returns(response);

        // Act
        var result = await _controller.UpdateBulkBpAsync("testUser", request);

        // Assert
        var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
        okResult.Value.Should().BeEquivalentTo(response);
        var actualResponse = okResult.Value as List<BaseProjectEditResponse>;
        actualResponse.Should().NotBeNull();
    }


    [Fact]
    public async Task UpdateBulkBpAsync_ShouldSetUpdatedByCorrectly()
    {
        // Arrange
        var request = new BulkBPEditRequest
        {
            BaseProjectIds = new List<int> { 1, 2, 3 },
            Suffix = "Suffix",
            PurposeId = 1,
            DataTypeId = 2
        };

        _baseProjectServiceMock
            .Setup(s => s.UpdateBulkAsync(It.IsAny<BaseProjectBulkEdit>()))
            .ReturnsAsync(new List<BaseProject>());

        _mapperMock
            .Setup(m => m.Map<IReadOnlyList<BaseProjectEditResponse>>(It.IsAny<List<BaseProject>>()))
            .Returns(new List<BaseProjectEditResponse>());

        // Act
        await _controller.UpdateBulkBpAsync("testxyz", request);

        // Assert
        request.UpdatedBy.Should().Be("testxyz");
    }

    [Fact]
    public async Task UpdateBulkBpAsync_ServiceThrowsException_ReturnsInternalServerError()
    {
        // Arrange
        var request = new BulkBPEditRequest
        {
            BaseProjectIds = Enumerable.Range(1, 10).ToList()
        };
        var mappedEntity = new BaseProjectBulkEdit();

        _mapperMock.Setup(m => m.Map<BaseProjectBulkEdit>(request)).Returns(mappedEntity);
        _baseProjectServiceMock.Setup(s => s.UpdateBulkAsync(mappedEntity)).ThrowsAsync(new Exception("Service error"));

        // Act
        Func<Task> act = async () => await _controller.UpdateBulkBpAsync("testUser", request);

        // Assert
        await act.Should().ThrowAsync<Exception>().WithMessage("Service error");
    }


    [Fact]
    public async Task UpdateBulkBpAsync_ShouldReturnEmptyList_WhenServiceReturnsEmpty()
    {
        // Arrange
        var request = new BulkBPEditRequest
        {
            BaseProjectIds = new List<int> { 1, 2 },
            Suffix = "Suffix",
            PurposeId = 1,
            DataTypeId = 2
        };

        _baseProjectServiceMock
            .Setup(s => s.UpdateBulkAsync(It.IsAny<BaseProjectBulkEdit>()))
            .ReturnsAsync(new List<BaseProject>());

        _mapperMock
            .Setup(m => m.Map<IReadOnlyList<BaseProjectEditResponse>>(It.IsAny<List<BaseProject>>()))
            .Returns(new List<BaseProjectEditResponse>());

        // Act
        var result = await _controller.UpdateBulkBpAsync("testUser", request);

        // Assert
        var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
        ((List<BaseProjectEditResponse>)okResult.Value).Should().BeEmpty();
    }

}
