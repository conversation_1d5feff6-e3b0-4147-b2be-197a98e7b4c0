﻿using WireMock.Server;

namespace DWH.ProjectServices.API.IntegrationTests.MockService;

public class DateAPIServer
{
    public WireMockServer Server { get; }

    public DateAPIServer()
    {
        Server = WireMockServer.Start();
        ConfigureWireMockStubs(Server);
    }

    private void ConfigureWireMockStubs(WireMockServer server)
    {
        server
            .Given(WireMock.RequestBuilders.Request.Create()
                .WithPath("/api/v1/Periods/current")
                .WithParam("periodicityId", "1")
                .UsingGet())
            .RespondWith(WireMock.ResponseBuilders.Response.Create()
                .WithStatusCode(200)
                .WithBody(@"[
                {
                    ""Id"": ""1"",
                    ""Name"": ""Period 1""
                },
                {
                    ""Id"": ""2"",
                    ""Name"": ""Period 2""
                }
            ]"));

        server
            .Given(WireMock.RequestBuilders.Request.Create()
                .WithPath("/api/v1/Periods/current")
                .WithParam("periodicityId", "4")
                .UsingGet())
            .RespondWith(WireMock.ResponseBuilders.Response.Create()
                .WithStatusCode(200)
                .WithBody(@"[
                {
                    ""Id"": ""1"",
                    ""Name"": ""Period 1""
                },
                {
                    ""Id"": ""2"",
                    ""Name"": ""Period 2""
                },
                {
                    ""Id"": ""3"",
                    ""Name"": ""Period 3""
                },
                {
                    ""Id"": ""4"",
                    ""Name"": ""Period 4""
                },
                {
                    ""Id"": ""5"",
                    ""Name"": ""Period 5""
                },
                {
                    ""Id"": ""6"",
                    ""Name"": ""Period 6""
                },
                {
                    ""Id"": ""7"",
                    ""Name"": ""Period 7""
                },
                {
                    ""Id"": ""8"",
                    ""Name"": ""Period 8""
                },
                {
                    ""Id"": ""9"",
                    ""Name"": ""Period 9""
                },
                {
                    ""Id"": ""10"",
                    ""Name"": ""Period 10""
                },
                {
                    ""Id"": ""11"",
                    ""Name"": ""Period 11""
                },
                {
                    ""Id"": ""12"",
                    ""Name"": ""Period 12""
                }
            ]"));

        server
            .Given(WireMock.RequestBuilders.Request.Create()
                .WithPath("/api/v1/Periods/current")
                .WithParam("periodicityId", "999")
                .UsingGet())
            .RespondWith(WireMock.ResponseBuilders.Response.Create()
                .WithStatusCode(200)
                .WithBody("[]")
                .WithHeader("Content-Type", "application/json"));

        server
            .Given(WireMock.RequestBuilders.Request.Create()
                .WithPath("/api/v1/Periods/current")
                .WithParam("periodicityId", "500")
                .UsingGet())
            .RespondWith(WireMock.ResponseBuilders.Response.Create()
                .WithStatusCode(500)
                .WithBody("Internal Server Error"));
    }

    public void Dispose()
    {
        Server.Stop();
        Server.Dispose();
    }
}
