﻿using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using FluentAssertions;

namespace DWH.ProjectServices.API.UnitTests.Models;

public class BaseProjectUTest
{
    [Fact]
    public void When_ValidParameters_Expect_NotNullInstance()
    {
        // Arrange
        var instance = new BaseProjectEntity
        {
            Id = 1,
            Name = "Test Project",
            TypeId = 2,
            PanelId = 3,
            DataTypeId = 4,
            PurposeId = 5,
            PeriodicityId = 6,
            CountryId = 7,
            IsRelevantForReportingEnabled = true,
            CreatedBy = "User",
            CreatedWhen = DateTime.UtcNow,
            UpdatedBy = "User",
            UpdatedWhen = DateTime.UtcNow.AddDays(1),
            DeletedBy = null,
            DeletedWhen = null,
            Deleted = false,
            ProductGroups = new List<BaseProjectProductGroupEntity>(),
            Predecessors = new List<BaseProjectPredecessorEntity>(),
            QCProjects = new QCProjectEntity(),
            DataType = new BaseProjectDataType(),
            Purpose = new BaseProjectPurpose()
        };

        // Act & Assert
        instance.Should().NotBeNull();
    }

    [Theory]
    [InlineData(null, "- copy")]
    [InlineData("", "- copy")]
    [InlineData("   ", "- copy")]
    [InlineData("test", "test - copy")]
    [InlineData("existing suffix", "existing suffix - copy")]
    public void GenerateCopySuffix_Should_HandleDifferentSuffixValues(string originalSuffix, string expectedSuffix)
    {
        // Arrange
        const int maxLength = 40;
        const string copyText = " - copy";

        // Act - Simulate the GenerateCopySuffix logic
        string actualSuffix;
        if (string.IsNullOrWhiteSpace(originalSuffix))
        {
            actualSuffix = "- copy";
        }
        else
        {
            string newSuffix = originalSuffix + copyText;
            if (newSuffix.Length > maxLength)
            {
                actualSuffix = newSuffix.Substring(0, maxLength);
            }
            else
            {
                actualSuffix = newSuffix;
            }
        }

        // Assert
        actualSuffix.Should().Be(expectedSuffix);
        actualSuffix.Length.Should().BeLessOrEqualTo(maxLength);
    }
}
