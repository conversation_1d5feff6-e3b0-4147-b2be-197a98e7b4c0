﻿using System.Net;
using System.Text.Json;
using DWH.ProjectServices.API.Models;
using Microsoft.AspNetCore.Mvc.Testing;

namespace DWH.ProjectServices.API.IntegrationTests.Presentation.Controllers;

public class ProjectSubTypeControllerCTest : IClassFixture<WebApplicationFactory<Program>>
{
    private const string PATH = "api/v1/ProjectSubTypes";
    private readonly WebApplicationFactory<Program> _factory;
    private const string EXPECTED_MEDIA_TYPE = "application/json";

    public ProjectSubTypeControllerCTest(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
    }

    [Fact]
    public async Task GetAllAsync_ProjectSubType_Returns_Success_Response()
    {
        var client = _factory.CreateClient();
        var response = await client.GetAsync(PATH);

        var responseContent = await response.Content.ReadAsStringAsync();
        var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
        var result = JsonSerializer.Deserialize<IEnumerable<ProjectSubType>>(responseContent, options);

        response.StatusCode.Should().Be(HttpStatusCode.OK);
        response.Content.Headers.ContentType?.MediaType.Should().Be(EXPECTED_MEDIA_TYPE);
        responseContent.Should().NotBeNull();
        result.Should().NotBeNull();
    }
}
