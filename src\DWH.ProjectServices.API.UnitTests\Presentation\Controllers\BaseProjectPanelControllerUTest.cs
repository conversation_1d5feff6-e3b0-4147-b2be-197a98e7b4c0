﻿using AutoMapper;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Controllers;
using DWH.ProjectServices.API.Services.Interfaces;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Moq;

namespace DWH.ProjectServices.API.UnitTests.Presentation.Controllers;

public class BaseProjectPanelControllerTests
{
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<IBaseProjectPanelService> _baseProjectPanelServiceMock;
    private readonly BaseProjectPanelsController _controller;

    public BaseProjectPanelControllerTests()
    {
        _mapperMock = new Mock<IMapper>();
        _baseProjectPanelServiceMock = new Mock<IBaseProjectPanelService>();
        _controller = new BaseProjectPanelsController(_baseProjectPanelServiceMock.Object);
    }

    [Fact]
    public async Task GetAllAsync_When_CalledForNotEmptyResult_Expect_200Response()
    {
        // Arrange
        var expectedResult = new List<BaseProjectPanel>
        {
            new BaseProjectPanel(),
            new BaseProjectPanel()
        };

        _baseProjectPanelServiceMock
            .Setup(s => s.GetAllAsync())
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAllAsync();

        // Assert
        result.Should().BeOfType<OkObjectResult>()
            .Which.Value.Should().BeEquivalentTo(expectedResult);
    }

    [Fact]
    public async Task GetAllAsync_When_ServiceReturnsNull_Expect_404NotFound()
    {
        // Arrange
        List<BaseProjectPanel> expectedResult = null;

        _baseProjectPanelServiceMock
            .Setup(s => s.GetAllAsync())
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAllAsync();

        // Assert
        result.Should().BeOfType<NotFoundResult>();
    }


}
