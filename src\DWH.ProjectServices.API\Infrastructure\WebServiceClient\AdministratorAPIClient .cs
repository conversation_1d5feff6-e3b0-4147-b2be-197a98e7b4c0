﻿using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Constants;
using DWH.ProjectServices.API.Services.Helper.Interface;

namespace DWH.ProjectServices.API.Infrastructure.WebServiceClient;

public class AdministratorAPIClient : BaseApiClient, IAdministratorAPIClient
{
    public AdministratorAPIClient(
        IHttpClientFactory httpClientFactory,
        ITokenService tokenService,
        ILogger<AdministratorAPIClient> logger,
        IPollyPolicyHelper pollyHelper)
        : base(httpClientFactory, tokenService, logger, pollyHelper)
    {
    }

    public async Task<ServiceResponse<T>> PostAsync<T>(string requestUri, StringContent requestContent)
    {
        var client = await GetHttpClientAsync("AdministratorAPI", AppConstants.AdminstratorAPI);
        return await ExecuteWithPoliciesAsync<T>(() => client.PostAsync(requestUri, requestContent), requestUri);
    }
}
