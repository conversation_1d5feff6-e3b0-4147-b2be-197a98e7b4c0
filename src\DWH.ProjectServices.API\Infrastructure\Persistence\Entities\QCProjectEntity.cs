﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Entities;

[Table("QCProject")]
public class QCProjectEntity
{


    [Key]
    public int Id { get; set; }
    public int BaseProjectId { get; set; }
    public int? ResetCorrectionTypeId { get; set; }

    public bool? IsAutoLoad { get; set; }

    public int? SQCMode { get; set; }

    public bool? IsAutomatedPriceCheck { get; set; }
    public ICollection<QCPeriodEntity> QCPeriods { get; set; }
}
