﻿using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations;

public class FactPdOutItmConfigurations : IEntityTypeConfiguration<FactPdOutItm>
{
    public void Configure(EntityTypeBuilder<FactPdOutItm> builder)
    {
        builder.ToTable(Constants.FACT_PD_OUT_ITM, Constants.DWH_DATA);
        builder.Property(p => p.Id).HasColumnName("ITEM_ID");
        builder.Property(p => p.BaseProjectId).HasColumnName("PROJECT_ID");
        builder.Property(p => p.ProductGroupId).HasColumnName("PRODUCTGROUP_ID");

    }
}
