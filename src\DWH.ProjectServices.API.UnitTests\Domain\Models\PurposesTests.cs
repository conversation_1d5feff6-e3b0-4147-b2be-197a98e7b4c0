﻿using AutoFixture;
using DWH.ProjectServices.API.Domain.Models;
using FluentAssertions;

namespace DWH.ProjectServices.API.UnitTests.Domain.Models;

public class PurposesTests
{
    private readonly Fixture _fixture;
    public PurposesTests()
    {
        _fixture = new Fixture();
    }

    [Fact]
    public void IdProperty_CanBeSetAndRetrieved()
    {
        // Arrange
        var purpose = new Purposes();
        var id = _fixture.Create<int>();

        // Act
        purpose.Id = id;

        // Assert
        purpose.Id.Should().Be(id);
    }

    [Fact]
    public void NameProperty_CanBeSetAndRetrieved()
    {
        // Arrange
        var purpose = new Purposes();
        var name = _fixture.Create<string>();

        // Act
        purpose.Name = name;

        // Assert
        purpose.Name.Should().Be(name);
    }
}
