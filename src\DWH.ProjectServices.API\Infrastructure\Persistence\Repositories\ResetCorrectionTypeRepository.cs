﻿using AutoMapper;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories;

public class ResetCorrectionTypeRepository : IResetCorrectionTypeRepository
{
    private readonly PostgreSqlDbContext _postdbContext;
    private readonly IMapper _mapper;

    public ResetCorrectionTypeRepository(PostgreSqlDbContext postdbContext, IMapper mapper)
    {
        _postdbContext = postdbContext;
        _mapper = mapper;
    }
    public async Task<IReadOnlyCollection<ResetCorrectionTypes>> GetAllAsync()
    {
        var resetCorrectionTypes = await _postdbContext.ResetCorrectionTypes.ToListAsync();
        if (!resetCorrectionTypes.Any())
            throw new EntityNotExistsException($"ResetCorrectionType Not Found ");

        return _mapper.Map<IReadOnlyCollection<ResetCorrectionTypes>>(resetCorrectionTypes); ;
    }

}
