﻿using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace DWH.ProjectServices.API.Models;

[Keyless]
public class DomainProductGroup
{
    public DomainProductGroup(int domainProductGroupId, string domainProductGroupDDesc, string sectorSDesc)
    {
        DomainProductGroupId = domainProductGroupId;
        DomainProductGroupDDesc = domainProductGroupDDesc;
        SectorSDesc = sectorSDesc;
    }

    [Column("DOMAIN_PRODUCTGROUP_ID")]
    public int DomainProductGroupId { get; }
    [Column("DOMAIN_PRODUCTGROUP_DDESC")]
    public string DomainProductGroupDDesc { get; }
    [Column("SECTOR_SDESC")]
    public string SectorSDesc { get; }
}
