﻿using System.Text.RegularExpressions;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;

namespace DWH.ProjectServices.API.Middleware;

public class CountryFilter
{
    private readonly RequestDelegate _next;

    public CountryFilter(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        if (context.Request.Headers.TryGetValue("Custom-Countryid", out var countryIdsHeader))
        {
            var countryIdArray = countryIdsHeader.ToString().Split(',', StringSplitOptions.RemoveEmptyEntries);
            context.Items["CountryIds"] = countryIdArray;

            context.Request.EnableBuffering();
            var bodyStr = await new System.IO.StreamReader(context.Request.Body).ReadToEndAsync();
            context.Request.Body.Position = 0;
            if (bodyStr.Length > 0)
            {
                string pattern = @"(?<!""name"":\s*)""(\d+)""";

                // Replace matched quoted numbers with unquoted numbers
                bodyStr = Regex.Replace(bodyStr, pattern, "$1", RegexOptions.None, TimeSpan.FromSeconds(2));

                var baseProjectListRequest = System.Text.Json.JsonSerializer.Deserialize<BaseProjectListRequest>(bodyStr, new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true, AllowTrailingCommas = true });
                if (baseProjectListRequest.CountryIds != null)
                {
                    baseProjectListRequest.CountryIds = baseProjectListRequest.CountryIds
                        .Where(id => countryIdArray.Contains(id.ToString()))
                        .ToArray();
                }

                context.Items["FilteredBaseProjectListRequest"] = baseProjectListRequest;
            }
        }
        else
        {
            context.Items["FilteredBaseProjectListRequest"] = null;
            context.Items["CountryIds"] = null;
        }

        await _next(context);
    }
}
