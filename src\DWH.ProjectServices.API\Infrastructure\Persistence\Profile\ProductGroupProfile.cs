﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Models.Dtos;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Profile;

public class ProductGroupProfile : AutoMapper.Profile
{
    public ProductGroupProfile()
    {
        CreateMap<BaseProjectProductGroupEntity, BaseProjectProductGroup>().ReverseMap();
        CreateMap<ProductGroup, ProductGroupResponse>().ReverseMap();
    }
}
