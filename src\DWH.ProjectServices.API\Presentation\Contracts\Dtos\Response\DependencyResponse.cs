﻿namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;

public class DependencyResponse
{
    public DependencyResponse(string baseProjectId, string qcProjectId, int statusCode, string statusMsg, ProjectDependencies dependency)
    {
        BaseProjectId = baseProjectId;
        QCProjectId = qcProjectId;
        StatusCode = statusCode;
        StatusMsg = statusMsg;
        Dependency = dependency;
    }
    public string BaseProjectId { get; set; }
    public string QCProjectId { get; set; }

    public int StatusCode { get; set; }
    public string StatusMsg { get; set; }

    public ProjectDependencies Dependency { get; set; }
}
