﻿using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations;

public class RBProjectConfigurations : IEntityTypeConfiguration<RBProject>
{
    public void Configure(EntityTypeBuilder<RBProject> builder)
    {
        builder.ToTable(Constants.ADM_PRJ_RBPROJECT, Constants.DWH_META);
        builder.Property(p => p.Id).HasColumnName("RBPROJECTID");
        builder.Property(p => p.Deleted).HasColumnName("DELETED");
        builder.Property(p => p.Name).HasColumnName("RBPROJECTNAME");
        builder.Property(p => p.PanelTypeId).HasColumnName("PANELTYPEID");

    }
}
