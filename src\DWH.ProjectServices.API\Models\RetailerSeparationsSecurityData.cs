﻿using DWH.ProjectServices.API.Models.Interfaces;

namespace DWH.ProjectServices.API.Models;

public class RetailerSeparationsSecurityData : IProjectServicesData
{
    public Guid Id { get; set; }
    public object SyncingEntityId { get; set; }
    public int SourceProjectId { get; set; }
    public int TargetProjectId { get; set; }
    public int TypeId { get; set; }
    public string Username { get; set; }
    public int IndexSourceBP { get; set; }
    public int TotalSourceBP { get; set; }
    public string JiraId { get; set; }
    public int RetailerSeparationRequestId { get; set; }
}
