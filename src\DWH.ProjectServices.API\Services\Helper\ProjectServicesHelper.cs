﻿using System.Text;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Helper.Interface;
using Newtonsoft.Json;

namespace DWH.ProjectServices.API.Services.Helper;

public class ProjectServicesHelper : IProjectServicesHelper
{
    private const string API_ENDPOINT_POST = "/api/v1/retailerseperation/IRSeparationBaseProject";
    private readonly IProjectServicesApiClient _projectServicesApiClient;

    public ProjectServicesHelper(IProjectServicesApiClient projectServicesApiClient)
    {
        _projectServicesApiClient = projectServicesApiClient;
    }

    public async Task PerformRetailerSeparation(IRSeparationBaseProjectRequest request, string userName)
    {
        var requestContent = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json");

        await _projectServicesApiClient.PostAsync<IRSeparationResponseBaseProject>(API_ENDPOINT_POST, requestContent, userName);

    }


}
