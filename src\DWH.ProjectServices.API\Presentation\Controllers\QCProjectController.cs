﻿using System.ComponentModel.DataAnnotations;
using AutoMapper;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace DWH.ProjectServices.API.Presentation.Controllers;

public class QCProjectsController : ApiController
{
    private readonly IMapper _mapper;
    private readonly IQCProjectService _qcProjectService;

    public QCProjectsController(IMapper mapper, IQCProjectService qcProjectService)
    {
        _mapper = mapper;
        _qcProjectService = qcProjectService;
    }


    /// <summary>
    /// Update a QCProject 
    /// </summary>
    [HttpPut("{qcProjectid}")]
    [Consumes("application/json")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(QCProjectEditResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> UpdateAsync([FromHeader(Name = "Custom-Countryid")] string countryIds,
    [Required] [FromHeader(Name = "userName")]
    string userName, int qcProjectid, QCProjectEditRequest updateQCProjectRequest)
    {
        if (!string.IsNullOrEmpty(countryIds))
        {
            var qcProjectCountryModel = new QCProjectCountries();
            qcProjectCountryModel.CountryIds = countryIds.Split(',').Select(int.Parse).ToArray();
            qcProjectCountryModel.QCProjectIds = new int[] { qcProjectid };
            var countryResult = await _qcProjectService.GetAsync(qcProjectCountryModel);
            if (countryResult.Count == 0)
            {
                return StatusCode(StatusCodes.Status403Forbidden);
            }
        }
        updateQCProjectRequest.UpdatedBy = userName;
        var qcProject = _mapper.Map<QCProjectUpdates>(updateQCProjectRequest);
        var response = await _qcProjectService.UpdateAsync(qcProjectid, qcProject);
        var result = _mapper.Map<QCProjectEditResponse>(response);
        return OkOrEmpty(result);
    }

    /// <summary>
    /// Return Authorized QCProjectIds
    /// </summary>
    [HttpPost("qcproject")]
    [Consumes("application/json")]
    [ActionName(nameof(GetQCProjectAsync))]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(List<int>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetQCProjectAsync(QCProjectCountryRequest qcProjectCountryRequest)
    {
        var qcProjectCountryModel = _mapper.Map<QCProjectCountries>(qcProjectCountryRequest);
        var result = await _qcProjectService.GetAsync(qcProjectCountryModel);
        if (result.Count == 0)
        {
            return StatusCode(StatusCodes.Status403Forbidden);
        }
        return OkOrEmptyList(result);
    }

}
