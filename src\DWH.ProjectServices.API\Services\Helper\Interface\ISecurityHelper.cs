﻿using DWH.ProjectServices.API.Domain.Models;

namespace DWH.ProjectServices.API.Services.Helper.Interface;

public interface ISecurityHelper
{

    Task<AssignUsersToProjectResponse> AssignUsersToProject(AssignUsersToProjectDetails assignUsersToProjectDetails, string userName);
    Task<ProjectUserDetails> GetUsersByProjectId(int qcProjectId);
    Task<bool> AssignUsersToNewProject(int sourceProjectId, int targetProjectId, string userName, int ProjectTypeId);
    Task<BulkCopyProjectUserResponse> BulkCopyProjectUsersAsync(BulkCopyProjectUserRequest bulkCopyRequest, string userName, string countryIds);
}
