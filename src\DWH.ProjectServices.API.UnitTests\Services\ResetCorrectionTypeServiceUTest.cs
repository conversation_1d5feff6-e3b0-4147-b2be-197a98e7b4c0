﻿using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Services;
using FluentAssertions;
using Moq;

namespace DWH.ProjectServices.API.UnitTests.Services;

public class ResetCorrectionTypeServiceTests
{
    private readonly Mock<IResetCorrectionTypeRepository> _resetCorrectionTypeRepositoryStub;
    private readonly ResetCorrectionTypeService _service;

    public ResetCorrectionTypeServiceTests()
    {
        _resetCorrectionTypeRepositoryStub = new Mock<IResetCorrectionTypeRepository>();
        _service = new ResetCorrectionTypeService(_resetCorrectionTypeRepositoryStub.Object);
    }

    [Fact]
    public async Task GetAllAsync_WhenCalled_WithData_ReturnsResetCorrectionTypes()
    {
        // Arrange
        var ResetCorrectionTypeEntities = new List<ResetCorrectionTypes>
        {
            new ResetCorrectionTypes { Id = 1, Name = "Type1" },
            new ResetCorrectionTypes { Id = 2, Name = "Type2" }
        };

        var expectedResetCorrectionTypes = new List<ResetCorrectionTypes>
        {
            new ResetCorrectionTypes { Id = 1, Name = "Type1" },
            new ResetCorrectionTypes { Id = 2, Name = "Type2" }
        };

        _resetCorrectionTypeRepositoryStub
            .Setup(repo => repo.GetAllAsync())
            .ReturnsAsync(ResetCorrectionTypeEntities);

        // Act
        var result = await _service.GetAllAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(expectedResetCorrectionTypes);
    }

    [Fact]
    public async Task GetAllAsync_WhenCalled_WithNoData_ReturnsEmptyCollection()
    {
        // Arrange
        var emptyResetCorrectionTypeEntities = new List<ResetCorrectionTypes>();

        _resetCorrectionTypeRepositoryStub
            .Setup(repo => repo.GetAllAsync())
            .ReturnsAsync(emptyResetCorrectionTypeEntities);

        // Act
        var result = await _service.GetAllAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task GetAllAsync_WhenCalled_AndRepositoryThrowsException_ThrowsException()
    {
        // Arrange
        _resetCorrectionTypeRepositoryStub
            .Setup(repo => repo.GetAllAsync())
            .ThrowsAsync(new EntityNotExistsException($"ResetCorrectionType Not Found "));

        // Act
        var act = async () => await _service.GetAllAsync();

        // Assert
        await act.Should().ThrowAsync<EntityNotExistsException>();
    }


}
