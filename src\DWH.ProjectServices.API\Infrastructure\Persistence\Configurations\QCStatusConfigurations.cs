﻿using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations;

public class QCStatusConfigurations : IEntityTypeConfiguration<QCStatus>
{
    public void Configure(EntityTypeBuilder<QCStatus> builder)
    {

        builder.ToTable(Constants.ADM_PRJ_QCPROJECT_PERIOD, Constants.DWH_META);
        builder.Property(p => p.BaseProjectId).HasColumnName("BASEPROJECTID");
        builder.Property(p => p.QCProjectId).HasColumnName("QCPROJECTID");
        builder.Property(p => p.RepPeriodId).HasColumnName("REPPERIODID");
        builder.Property(p => p.Status).HasColumnName("STATUS");
        builder.Property(p => p.Deleted).HasColumnName("DELETED");
    }
}
