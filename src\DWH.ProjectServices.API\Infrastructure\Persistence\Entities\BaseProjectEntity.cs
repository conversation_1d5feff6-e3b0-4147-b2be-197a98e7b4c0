﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Entities;


[Table("BaseProject")]
public class BaseProjectEntity
{

    [Key]
    public int Id { get; set; }
    [MaxLength(40)]
    public string Name { get; set; }
    public int TypeId { get; set; }

    public int PanelId { get; set; }
    public int DataTypeId { get; set; }
    public int PurposeId { get; set; }
    public int PeriodicityId { get; set; }
    public int CountryId { get; set; }
    public bool IsRelevantForReportingEnabled { get; set; }
    public string CreatedBy { get; set; }
    public DateTimeOffset CreatedWhen { get; set; }
    public string UpdatedBy { get; set; }
    public DateTimeOffset? UpdatedWhen { get; set; }
    public string DeletedBy { get; set; }
    public DateTimeOffset? DeletedWhen { get; set; }
    public bool? Deleted { get; set; }
    public string Suffix { get; set; }

    public ICollection<BaseProjectProductGroupEntity> ProductGroups { get; set; }

    public ICollection<BaseProjectPredecessorEntity> Predecessors { get; set; }
    public QCProjectEntity QCProjects { get; set; }
    public BaseProjectDataType DataType { get; set; }
    public BaseProjectPurpose Purpose { get; set; }

}
