﻿using System.ComponentModel.DataAnnotations;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;

public class QCPeriodDeleteRequest : IValidatableObject
{
    [Required]
    public List<long> Ids { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();
        if (Ids.Count <= 0)
        {
            results.Add(new ValidationResult("Must have atleast one id to proceed"));
        }

        return results;
    }
}
