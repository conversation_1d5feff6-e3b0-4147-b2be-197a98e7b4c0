﻿using AutoMapper;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories;

public class BaseProjectPanelRepository : IBaseProjectPanelRepository
{
    private readonly PostgreSqlDbContext _postdbContext;
    private readonly IMapper _mapper;

    public BaseProjectPanelRepository(PostgreSqlDbContext postdbContext, IMapper mapper)
    {
        _postdbContext = postdbContext;
        _mapper = mapper;

    }
    public async Task<IReadOnlyCollection<BaseProjectPanel>> GetAllAsync()
    {
        var baseProjectPanelentities = await _postdbContext.BaseProjectPanels.ToListAsync();

        if (!baseProjectPanelentities.Any())
            throw new EntityNotExistsException("BaseProjectEntity Panels Not Found");

        return _mapper.Map<IReadOnlyCollection<BaseProjectPanel>>(baseProjectPanelentities);

    }
}
