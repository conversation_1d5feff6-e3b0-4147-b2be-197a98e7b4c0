﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DWH.ProjectServices.API.Migrations;

/// <inheritdoc />
public partial class RenameRetailerSeparation : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_RetailerSeperation_RetailerSeperationRequests_RetailerSeper~",
            table: "RetailerSeperation");

        migrationBuilder.DropForeignKey(
            name: "FK_RetailerSeperationRequestDetail_RetailerSeperationRequests_~",
            table: "RetailerSeperationRequestDetail");

        migrationBuilder.RenameTable(name: "RetailerSeperationRequests", newName: "RetailerSeperationRequest");

        migrationBuilder.AddForeignKey(
            name: "FK_RetailerSeperation_RetailerSeperationRequest_RetailerSepera~",
            table: "RetailerSeperation",
            column: "RetailerSeperationRequestId",
            principalTable: "RetailerSeperationRequest",
            principalColumn: "Id");

        migrationBuilder.AddForeignKey(
            name: "FK_RetailerSeperationRequestDetail_RetailerSeperationRequest_R~",
            table: "RetailerSeperationRequestDetail",
            column: "RetailerSeperationRequestId",
            principalTable: "RetailerSeperationRequest",
            principalColumn: "Id");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_QCPeriod_QCProject_QCProjectId",
            table: "QCPeriod");

        migrationBuilder.DropForeignKey(
            name: "FK_RetailerSeperation_RetailerSeperationRequest_RetailerSepera~",
            table: "RetailerSeperation");

        migrationBuilder.DropForeignKey(
            name: "FK_RetailerSeperationRequestDetail_RetailerSeperationRequest_R~",
            table: "RetailerSeperationRequestDetail");

        migrationBuilder.DropTable(
            name: "RetailerSeperationRequest");

        migrationBuilder.DropIndex(
            name: "IX_RetailerSeperationRequestDetail_RetailerSeperationRequestEn~",
            table: "RetailerSeperationRequestDetail");

        migrationBuilder.DropIndex(
            name: "IX_RetailerSeperation_RetailerSeperationRequestEntityId",
            table: "RetailerSeperation");

        migrationBuilder.DropColumn(
            name: "RetailerSeperationRequestEntityId",
            table: "RetailerSeperationRequestDetail");

        migrationBuilder.DropColumn(
            name: "RetailerSeperationRequestEntityId",
            table: "RetailerSeperation");

        migrationBuilder.AlterColumn<int>(
            name: "QCProjectId",
            table: "QCPeriod",
            type: "integer",
            nullable: true,
            oldClrType: typeof(int),
            oldType: "integer");

        migrationBuilder.AlterColumn<DateTimeOffset>(
            name: "CreatedWhen",
            table: "QCPeriod",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(2025, 1, 6, 7, 44, 9, 554, DateTimeKind.Unspecified).AddTicks(3174), new TimeSpan(0, 0, 0, 0, 0)),
            oldClrType: typeof(DateTimeOffset),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTimeOffset(new DateTime(2025, 1, 6, 12, 55, 20, 607, DateTimeKind.Unspecified).AddTicks(6261), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AlterColumn<DateTimeOffset>(
            name: "CreatedWhen",
            table: "BaseProject",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(2025, 1, 6, 7, 44, 9, 554, DateTimeKind.Unspecified).AddTicks(2246), new TimeSpan(0, 0, 0, 0, 0)),
            oldClrType: typeof(DateTimeOffset),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTimeOffset(new DateTime(2025, 1, 6, 12, 55, 20, 607, DateTimeKind.Unspecified).AddTicks(5518), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.CreateTable(
            name: "RetailerSeperationRequests",
            columns: table => new
            {
                Id = table.Column<int>(type: "integer", nullable: false)
                    .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                Extrapolation = table.Column<bool>(type: "boolean", nullable: false),
                FromPeriodId = table.Column<long>(type: "bigint", nullable: false),
                JiraId = table.Column<string>(type: "text", nullable: true),
                RequestStatusId = table.Column<int>(type: "integer", nullable: false),
                ResetCorrection = table.Column<bool>(type: "boolean", nullable: false),
                ToPeriodId = table.Column<long>(type: "bigint", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_RetailerSeperationRequests", x => x.Id);
            });

        migrationBuilder.CreateIndex(
            name: "IX_RetailerSeperationRequestDetail_RetailerSeperationRequestId",
            table: "RetailerSeperationRequestDetail",
            column: "RetailerSeperationRequestId");

        migrationBuilder.CreateIndex(
            name: "IX_RetailerSeperation_RetailerSeperationRequestId",
            table: "RetailerSeperation",
            column: "RetailerSeperationRequestId");

        migrationBuilder.AddForeignKey(
            name: "FK_QCPeriod_QCProject_QCProjectId",
            table: "QCPeriod",
            column: "QCProjectId",
            principalTable: "QCProject",
            principalColumn: "Id");

        migrationBuilder.AddForeignKey(
            name: "FK_RetailerSeperation_RetailerSeperationRequests_RetailerSeper~",
            table: "RetailerSeperation",
            column: "RetailerSeperationRequestId",
            principalTable: "RetailerSeperationRequests",
            principalColumn: "Id",
            onDelete: ReferentialAction.Cascade);

        migrationBuilder.AddForeignKey(
            name: "FK_RetailerSeperationRequestDetail_RetailerSeperationRequests_~",
            table: "RetailerSeperationRequestDetail",
            column: "RetailerSeperationRequestId",
            principalTable: "RetailerSeperationRequests",
            principalColumn: "Id",
            onDelete: ReferentialAction.Cascade);
    }
}
