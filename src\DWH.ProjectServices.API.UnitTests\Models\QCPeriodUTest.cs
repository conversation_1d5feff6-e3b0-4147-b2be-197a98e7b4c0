﻿using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using FluentAssertions;


namespace DWH.ProjectServices.API.UnitTests.Models;

public class QCPeriodUTest
{
    [Fact]
    public void When_ValidParameters_Expect_NotNullInstance()
    {
        // Arrange
        var instance = new QCPeriodEntity
        {
            Id = 1,
            QCProjectId = 1,
            PeriodId = 1,
            CreatedBy = "",
            CreatedWhen = DateTime.UtcNow,
            UpdatedBy = "",
            UpdatedWhen = DateTime.UtcNow.AddDays(1),
            Periods = new List<PeriodEntity>(),
            StockInitialization = new StockInitializationEntity()
        };

        // Act & Assert
        instance.Should().NotBeNull();
    }
}
