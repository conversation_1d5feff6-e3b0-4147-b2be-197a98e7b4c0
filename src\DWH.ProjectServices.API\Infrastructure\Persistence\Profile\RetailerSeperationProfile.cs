﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Constants;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Profile;

public class RetailerSeperationProfile : AutoMapper.Profile
{
    public RetailerSeperationProfile()
    {
        CreateMap<RetailerSeperationCreateRequest, RetailerSeperationRequest>().ReverseMap();
        CreateMap<RetailerSeperationModelDto, RetailerSeperation>().ReverseMap();
        CreateMap<RetailerSeperationRequestDetailModelDto, RetailerSeperationRequestDetail>().ReverseMap();
        CreateMap<RetailerSeperationResponse, RetailerSeperationRequest>().ReverseMap();
        CreateMap<RetailerSeperationRequestEntity, RetailerSeperationRequest>().ReverseMap();
        CreateMap<RetailerSeperationRequestEntity, RetailerSeperationRequest>().ReverseMap();
        CreateMap<RetailerSeperationRequestEntity, RetailerSeperationRequest>().ReverseMap();
        CreateMap<RetailerSeperationEntity, RetailerSeperation>().ReverseMap();
        CreateMap<RetailerSeperationRequest, RetailerSeparationGetResponse>().ReverseMap();
        CreateMap<RetailerSeperationRequest, RetailerSeparationEditResponse>().ReverseMap();
        CreateMap<RetailerSeperationRequestDetailEntity, RetailerSeperationRequestDetail>().ReverseMap();

        CreateMap<RetailerSeperationEditRequest, RetailerSeperationRequest>()
        .ForMember(dest => dest.RetailerSeperations, opt => opt.MapFrom(src => src.RetailerSeperations));

        CreateMap<DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request.RetailerSeperationEditRequest, DWH.ProjectServices.API.Domain.Models.RetailerSeperationRequest>()
            .ForMember(dest => dest.RetailerSeperations, opt => opt.MapFrom(src => src.RetailerSeperations));

        CreateMap<RetailerSeperationsLists, RetailerSeperationRequest>().ReverseMap();
        CreateMap<RetailerSeperationListResponse, RetailerSeperationLists>().ReverseMap();
        CreateMap<RetailerSeperationResponse, RetailerSeperationResponse>()
        .ForMember(dest => dest.RequestStatus, opt => opt.MapFrom(src =>
            RetailerRequestConstants.StatusNames.ContainsKey(src.RequestStatusId)
                ? RetailerRequestConstants.StatusNames[src.RequestStatusId]
                : "Unknown"))
        .ForMember(dest => dest.RetailerSeperationRequestDetails, opt => opt.MapFrom((src, dest) =>
        {
            if (src.RetailerSeperationRequestDetails != null && src.RetailerSeperationRequestDetails.Any())
            {
                foreach (var detail in src.RetailerSeperationRequestDetails)
                {
                    detail.RequestStatus = RetailerRequestConstants.StatusNames.ContainsKey(detail.RequestStatusId)
                        ? RetailerRequestConstants.StatusNames[detail.RequestStatusId]
                        : "Unknown";
                }
            }
            return src.RetailerSeperationRequestDetails;
        }));


        CreateMap<RetailerSeperationsLists, RetailerSeperationListRequest>().ReverseMap();
        CreateMap<BaseProjectEntity, BaseProject>().ReverseMap();
        CreateMap<IRSeperationDeletes, IRSeparationSourceBaseprojectsDeleteRequest>().ReverseMap();
    }
}
