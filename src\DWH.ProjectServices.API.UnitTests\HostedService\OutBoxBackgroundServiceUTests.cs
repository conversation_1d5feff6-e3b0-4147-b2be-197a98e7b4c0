﻿using System.Reflection;
using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;

namespace DWH.ProjectServices.API.UnitTests.HostedService;

public class OutBoxBackgroundServiceUTests
{
    private readonly Mock<IServiceScopeFactory> _mockServiceScopeFactory;
    private readonly Mock<IServiceScope> _mockServiceScope;
    private readonly Mock<IServiceProvider> _mockServiceProvider;
    private readonly Mock<IOutBoxItemRepository> _mockOutBoxRepository;
    private readonly Mock<IRabbitMQSender> _mockRabbitMQSender;
    private readonly Mock<ILogger<OutBoxBackgroundService>> _mockLogger;
    private readonly OutBoxBackgroundService _backgroundService;
    IConfigurationRoot _configuration;

    public OutBoxBackgroundServiceUTests()
    {
        var configurationSettings = new Dictionary<string, string>
                                    {
                                        { "RetryLimit:Value", "3" },
                                        { "RetryLimit:Delay", "10" }
                                    };

        var configurationBuilder = new ConfigurationBuilder();
        configurationBuilder.AddInMemoryCollection(configurationSettings);
        _configuration = configurationBuilder.Build();

        _mockServiceScopeFactory = new Mock<IServiceScopeFactory>();
        _mockServiceScope = new Mock<IServiceScope>();
        _mockServiceProvider = new Mock<IServiceProvider>();
        _mockOutBoxRepository = new Mock<IOutBoxItemRepository>();
        _mockRabbitMQSender = new Mock<IRabbitMQSender>();
        _mockLogger = new Mock<ILogger<OutBoxBackgroundService>>();

        _mockServiceScope.Setup(x => x.ServiceProvider).Returns(_mockServiceProvider.Object);
        _mockServiceScopeFactory.Setup(x => x.CreateScope()).Returns(_mockServiceScope.Object);

        _mockServiceProvider.Setup(x => x.GetService(typeof(IOutBoxItemRepository)))
            .Returns(_mockOutBoxRepository.Object);
        _mockServiceProvider.Setup(x => x.GetService(typeof(IRabbitMQSender)))
            .Returns(_mockRabbitMQSender.Object);

        _backgroundService = new OutBoxBackgroundService(
            _mockServiceScopeFactory.Object,
            _mockLogger.Object,
            _mockRabbitMQSender.Object,
            _configuration
        );
    }

    [Fact]
    public async Task ExecuteAsync_ShouldRetrieveUnprocessedMessages()
    {
        // Arrange
        var messages = new List<OutBoxItemEntity>
        {
            new OutBoxItemEntity { Id = Guid.NewGuid(), TypeId = "BPSecurityCreate", Status = OutboxStatus.Pending }
        };

        _mockOutBoxRepository.Setup(repo => repo.GetUnprocessedMessagesAsync()).ReturnsAsync(messages);
        _mockOutBoxRepository.Setup(repo => repo.MarkMessageAsProcessedAsync(It.IsAny<Guid>())).Returns(Task.CompletedTask);
        _mockOutBoxRepository.Setup(repo => repo.CleanupOldProcessedMessagesAsync()).Returns(Task.CompletedTask);

        using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(500));

        // Act
        await _backgroundService.StartAsync(cts.Token);

        // Assert
        _mockOutBoxRepository.Verify(repo => repo.GetUnprocessedMessagesAsync(), Times.AtLeastOnce);
    }

    [Fact]
    public void GetRabbitMQSettings_ShouldThrowArgumentException_ForUnknownTypeId()
    {
        // Act
        Action act = () => _backgroundService.GetType()
            .GetMethod("GetRabbitMQSettings", BindingFlags.NonPublic | BindingFlags.Instance)
            .Invoke(_backgroundService, new object[] { "UnknownType" });

        // Assert
        act.Should().Throw<TargetInvocationException>()
            .WithInnerException<ArgumentException>()
            .WithMessage("Unknown TypeId: UnknownType");
    }

    [Fact]
    public async Task BackgroundService_ShouldRetry_On_Exception_Until_Limit()
    {
        // Arrange
        _mockServiceScopeFactory.Setup(s => s.CreateScope()).Throws(new Exception("Test Exception"));

        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(2));

        // Act
        var executeTask = Task.Run(() => _backgroundService.StartAsync(cts.Token));
        await Task.Delay(1000);
        cts.Cancel();

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                It.Is<LogLevel>(logLevel => logLevel == LogLevel.Error),
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((state, _) => state.ToString().Contains("Outbox Background Service error")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()
            ),
            Times.AtMost(3)
        );
    }

    [Fact]
    public async Task BackgroundService_ShouldResetExceptionCount_After_Delay()
    {
        // Arrange
        _mockServiceScopeFactory.Setup(s => s.CreateScope()).Throws(new Exception("Test Exception"));

        using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(2));

        // Act
        var executeTask = Task.Run(() => _backgroundService.StartAsync(cts.Token));
        await Task.Delay(3000);
        cts.Cancel();

        // Assert
        _mockServiceScopeFactory.Verify(s => s.CreateScope(), Times.AtLeast(3));
    }

    [Fact]
    public async Task BackgroundService_ShouldCreateScope_And_ProcessMessages()
    {
        // Arrange
        _mockServiceScopeFactory.Setup(s => s.CreateScope()).Returns(_mockServiceScope.Object);

        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(2));

        // Act
        var executeTask = Task.Run(() => _backgroundService.StartAsync(cts.Token));
        await Task.Delay(500);
        cts.Cancel();

        // Assert
        _mockServiceScopeFactory.Verify(s => s.CreateScope(), Times.AtLeastOnce());
    }

    [Fact]
    public async Task BackgroundService_ShouldCallCleanupMethod()
    {
        // Arrange
        _mockServiceScopeFactory.Setup(s => s.CreateScope()).Returns(_mockServiceScope.Object);

        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(1));

        // Act
        var executeTask = Task.Run(() => _backgroundService.StartAsync(cts.Token));
        await Task.Delay(500);
        cts.Cancel();

        // Assert
        _mockOutBoxRepository.Verify(repo =>
            repo.CleanupOldProcessedMessagesAsync(),
            Times.AtMost(1)); // May or may not be called depending on timing
    }
}

