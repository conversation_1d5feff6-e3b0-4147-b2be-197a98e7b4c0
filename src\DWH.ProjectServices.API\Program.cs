﻿using System.Text.Json.Serialization;
using BootstrapAPI;
using BootstrapAPI.Core;
using BootstrapAPI.Core.Db;
using BootstrapAPI.Tracing;
using DWH.ProjectServices.API;
using DWH.ProjectServices.API.Infrastructure.Configuration;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Configuration;
using DWH.ProjectServices.API.Infrastructure.SignalR;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient;
using DWH.ProjectServices.API.Services.Helper;
using DWH.ProjectServices.API.StartupExtensions;
using Microsoft.EntityFrameworkCore;
using OpenTelemetry.Trace;

var builder = WebApplication.CreateBuilder(args);
builder.SetupBootstrapCore();

InstrumentationFactory.Add(tracerProviderBuilder =>
    tracerProviderBuilder.AddEntityFrameworkCoreInstrumentation(options => options.SetDbStatementForText = true));


builder.Services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());

builder.Services.AddDbContext<OracleDbContext>(options =>
    options.UseOracle(builder.Configuration.GetConnection("ConnectionStrings:Oracle").ToString()));
builder.Services.Configure<RabbitMQSettings>(builder.Configuration.GetSection("ConnectionStrings:RabbitMQ"));

var postgresConnectionString = builder.Configuration["ConnectionStrings:PostgreSQL:DefaultConnection"].ToString() +
                                  builder.Configuration["ConnectionStrings:PostgreSQL:Password"].ToString();
builder.Services.AddDbContext<PostgreSqlDbContext>(options =>
    options.UseNpgsql(postgresConnectionString));

builder.Services.AddHostedService<OutBoxBackgroundService>();
builder.Services.AddHostedService<RetailerSeparationConsumerService>();

builder.Services.AddControllersWithViews(options =>
{
    options.Conventions.Add(new LowercaseControllerModelConvention());
});


builder.Services.AddControllers().AddJsonOptions(options =>
{
    options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
});

builder.Services.AddBootstrapApi<PostgreSqlDbContext>(builder.Configuration);
builder.Services.AddSignalR();
builder.Services.AddServices();
builder.Services.AddRepositories();
builder.Services.Configure<WebServiceClientOptions>(builder.Configuration.GetSection("WebServiceClient"));
builder.Services.Configure<OutboxCleanupOptions>(builder.Configuration.GetSection(OutboxCleanupOptions.SectionName));

var webServiceClientOptions = builder.Configuration.GetSection("WebServiceClient").Get<WebServiceClientOptions>();

builder.Services.AddHttpClient("DateApi", client =>
{
    client.BaseAddress = new Uri(webServiceClientOptions.BaseAddress.DateAPI);
});

builder.Services.AddHttpClient("AdministratorAPI", client =>
{
    client.BaseAddress = new Uri(webServiceClientOptions.BaseAddress.AdministratorAPI);
});

builder.Services.AddHttpClient("JiraAPI", client =>
{
    client.BaseAddress = new Uri(webServiceClientOptions.BaseAddress.JiraAPI);
});

builder.Services.AddHttpClient("QCSecurityAPI", client =>
{
    client.BaseAddress = new Uri(webServiceClientOptions.BaseAddress.ProjectServicesSecurityAPI);
});

builder.Services.AddHttpClient("RoleApi", client =>
{
    client.BaseAddress = new Uri(webServiceClientOptions.BaseAddress.RoleAPI);
});

builder.Services.AddHttpClient("ProjectServicesAPI", client =>
{
    client.BaseAddress = new Uri(webServiceClientOptions.BaseAddress.ProjectServicesAPI);
});

builder.Services.Configure<TokenSettings>(builder.Configuration.GetSection("TokenSettings"));
var app = builder.Build();
app.MapHub<NotificationHub>("/notificationHub");

app.UseCountryFilter();

AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);

app.UseBootstrapApi();

app.Run();

public partial class Program
{
    protected Program()
    {

    }
}

