﻿using AutoMapper;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Controllers;
using DWH.ProjectServices.API.Services.Interfaces;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Moq;

namespace DWH.ProjectServices.API.UnitTests.Presentation.Controllers;

public class ProjectSubTypeControllerTests
{
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<IProjectSubTypeService> _projectSubTypeServiceMock;
    private readonly ProjectSubTypesController _controller;

    public ProjectSubTypeControllerTests()
    {
        _mapperMock = new Mock<IMapper>();
        _projectSubTypeServiceMock = new Mock<IProjectSubTypeService>();
        _controller = new ProjectSubTypesController(_projectSubTypeServiceMock.Object, _mapperMock.Object);
    }

    [Fact]
    public async Task GetAsync_When_CalledForNotEmptyResult_Expect_200Response()
    {
        // Arrange
        var expectedResult = new List<ProjectSubTypes>
        {
            new ProjectSubTypes(1,"hbgygvg"),
            new ProjectSubTypes(2,"hbgygvasg"),

        };

        _projectSubTypeServiceMock
            .Setup(s => s.GetAllAsync())
            .ReturnsAsync(expectedResult); // Return the tuple with expected result and null exception

        // Act
        var result = await _controller.GetAllAsync();

        // Assert
        result.Should().BeOfType<OkObjectResult>()
            .Which.Value.Should().BeEquivalentTo(expectedResult);
    }

    [Fact]
    public async Task GetAllAsync_When_ServiceReturnsNull_Expect_404NotFound()
    {
        // Arrange
        List<ProjectSubTypes> expectedResult = null;

        _projectSubTypeServiceMock
            .Setup(s => s.GetAllAsync())
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetAllAsync();

        // Assert
        result.Should().BeOfType<NotFoundResult>();
    }
}
