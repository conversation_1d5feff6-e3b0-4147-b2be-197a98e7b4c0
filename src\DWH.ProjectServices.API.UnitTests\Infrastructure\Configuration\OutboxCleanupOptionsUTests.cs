﻿using DWH.ProjectServices.API.Infrastructure.Configuration;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace DWH.ProjectServices.API.UnitTests.Infrastructure.Configuration;

public class OutboxCleanupOptionsUTests
{
    [Fact]
    public void OutboxCleanupOptions_ShouldHaveCorrectSectionName()
    {
        // Assert
        OutboxCleanupOptions.SectionName.Should().Be("OutboxCleanup");
    }

    [Fact]
    public void OutboxCleanupOptions_ShouldHaveDefaultRetentionDays()
    {
        // Arrange & Act
        var options = new OutboxCleanupOptions();

        // Assert
        options.RetentionDays.Should().Be(14);
    }

    [Fact]
    public void OutboxCleanupOptions_ShouldBindFromConfiguration()
    {
        // Arrange
        var configurationSettings = new Dictionary<string, string>
        {
            { "OutboxCleanup:RetentionDays", "30" }
        };

        var configurationBuilder = new ConfigurationBuilder();
        configurationBuilder.AddInMemoryCollection(configurationSettings);
        var configuration = configurationBuilder.Build();

        var services = new ServiceCollection();
        services.Configure<OutboxCleanupOptions>(configuration.GetSection(OutboxCleanupOptions.SectionName));
        var serviceProvider = services.BuildServiceProvider();

        // Act
        var options = serviceProvider.GetRequiredService<IOptions<OutboxCleanupOptions>>().Value;

        // Assert
        options.RetentionDays.Should().Be(30);
    }

    [Fact]
    public void OutboxCleanupOptions_ShouldUseDefaultWhenConfigurationMissing()
    {
        // Arrange
        var configurationSettings = new Dictionary<string, string>();

        var configurationBuilder = new ConfigurationBuilder();
        configurationBuilder.AddInMemoryCollection(configurationSettings);
        var configuration = configurationBuilder.Build();

        var services = new ServiceCollection();
        services.Configure<OutboxCleanupOptions>(configuration.GetSection(OutboxCleanupOptions.SectionName));
        var serviceProvider = services.BuildServiceProvider();

        // Act
        var options = serviceProvider.GetRequiredService<IOptions<OutboxCleanupOptions>>().Value;

        // Assert
        options.RetentionDays.Should().Be(14); // Default value
    }
}
