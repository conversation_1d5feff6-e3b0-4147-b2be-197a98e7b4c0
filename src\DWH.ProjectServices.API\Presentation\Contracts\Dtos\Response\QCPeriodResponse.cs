﻿using DWH.ProjectServices.API.Domain.Models;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;

public class QCPeriodResponse
{
    public int Id { get; set; }
    public int QCProjectId { get; set; }
    public long PeriodId { get; set; }
    public int? Status { get; set; }
    public ICollection<Period> Periods { get; set; }
    public StockInitialization StockInitialization { get; set; }
}

