﻿using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Constants;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Services.Constants;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace DWH.ProjectServices.API.Infrastructure.RabbitMQ
{
    public class OutBoxBackgroundService : BackgroundService
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ILogger<OutBoxBackgroundService> _logger;
        private readonly IRabbitMQSender _rabbitMqSender;
        private IConfiguration _configuration;
        private int _exceptionCount = 0;

        public OutBoxBackgroundService(IServiceScopeFactory serviceScopeFactory, ILogger<OutBoxBackgroundService> logger, IRabbitMQSender rabbitMqSender, IConfiguration configuration)
        {
            _serviceScopeFactory = serviceScopeFactory;
            _logger = logger;
            _rabbitMqSender = rabbitMqSender;
            _configuration = configuration;
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            int retryMaxLimit = _configuration.GetValue("RetryLimit:Value", AppConstants.DefaultRetryLimit);
            int delayMaxLimit = _configuration.GetValue("RetryLimit:Delay", AppConstants.DefaultDelayLimit);
            int outboxRetentionValue = _configuration.GetValue("OutboxCleanup:RetentionValue", 2);
            string outboxRetentionUnit = _configuration.GetValue("OutboxCleanup:RetentionUnit", "Months");
            var lastCleanupTime = DateTime.MinValue;


            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using (var scope = _serviceScopeFactory.CreateScope())
                    {
                        var repository = scope.ServiceProvider.GetRequiredService<IOutBoxItemRepository>();
                        var messages = await repository.GetUnprocessedMessagesAsync();
                        foreach (var message in messages)
                        {
                           
                            try
                            {
                                var (exchange, routingKey) = GetRabbitMQSettings(message.TypeId);

                                if (message.TypeId == ProjectMessageType.RetailerSeparationRequest.ToString())
                                {
                                    var payload = JsonSerializer.Deserialize<RetailerSeparationsData>(message.Payload);
                                    await _rabbitMqSender.SendToRabbitMQ(exchange, routingKey, payload);

                                    await repository.MarkMessageAsProcessedAsync(message.Id);
                                }
                                else if (message.TypeId == ProjectMessageType.RetailerSeparationSecurityRequest.ToString())
                                {
                                    var payload = JsonSerializer.Deserialize<RetailerSeparationsSecurityData>(message.Payload);
                                    await _rabbitMqSender.SendToRabbitMQ(exchange, routingKey, payload);

                                    await repository.MarkMessageAsProcessedAsync(message.Id);
                                }
                                else
                                {
                                    var payload = JsonSerializer.Deserialize<ProjectServicesData>(message.Payload);

                                    await _rabbitMqSender.SendToRabbitMQ(exchange, routingKey, payload);

                                    await repository.MarkMessageAsProcessedAsync(message.Id);
                                }
                                
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError($"Error processing message {message.Id}: {ex.Message}");
                            }
                        }
                        if ((DateTime.UtcNow - lastCleanupTime).TotalHours >= 24)
                        {
                            _logger.LogInformation("Running Outbox Cleanup Task...");
                            await repository.CleanupOldProcessedMessagesAsync(outboxRetentionValue, outboxRetentionUnit);
                            lastCleanupTime = DateTime.UtcNow;
                        }
                    }
                    await Task.CompletedTask;
                    await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);

                }
                catch (Exception ex)
                {
                    if (_exceptionCount < retryMaxLimit)
                    {
                        _logger.LogError($"Outbox Background Service error: {ex.Message}");
                        _exceptionCount++;
                    }
                    else
                    {
                        await Task.Delay(TimeSpan.FromMinutes(delayMaxLimit), stoppingToken);
                        _exceptionCount = 0;
                    }
                }
            }

        }


        private (string Exchange, string RoutingKey) GetRabbitMQSettings(string typeId)
        {
            return typeId switch
            {
                "BaseProjectCreate" => (BaseProjectConstants.BaseProjectExchange, RMQConstants.CreateRoutingKey),
                "BaseProjectUpdate" => (BaseProjectConstants.BaseProjectExchange, RMQConstants.UpdateRoutingKey),
                "BaseProjectDelete" => (BaseProjectConstants.BaseProjectExchange, RMQConstants.DeleteRoutingKey),
                "QCProjectCreate" => (QCProjectConstants.QCProjectExchange, RMQConstants.CreateRoutingKey),
                "QCProjectUpdate" => (QCProjectConstants.QCProjectExchange, RMQConstants.UpdateRoutingKey),
                "QCProjectDelete" => (QCProjectConstants.QCProjectExchange, RMQConstants.DeleteRoutingKey),
                "QCPeriodCreate" => (QCPeriodConstants.QCPeriodExchange, RMQConstants.CreateRoutingKey),
                "QCPeriodUpdate" => (QCPeriodConstants.QCPeriodExchange, RMQConstants.UpdateRoutingKey),
                "QCPeriodDelete" => (QCPeriodConstants.QCPeriodExchange, RMQConstants.DeleteRoutingKey),
                "RetailerSeparationRequest" => (RetailerSeparationConstants.RetailerSeparationExchange, RMQConstants.RetailerSeparationProjectServicesRoutingKey),
                "RetailerSeparationSecurityRequest" => (ProjectSecurityConstants.ProjectServicesSecurityExchange, RMQConstants.ProjectServicesSecurityRoutingKey),
                _ => throw new ArgumentException($"Unknown TypeId: {typeId}")
            };
        }

    }
}
