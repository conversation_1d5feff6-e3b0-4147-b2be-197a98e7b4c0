﻿using System.Linq.Expressions;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Middleware;
using DWH.ProjectServices.API.Services;
using DWH.ProjectServices.API.Services.Helper;
using DWH.ProjectServices.API.Services.Helper.Interface;
using DWH.ProjectServices.API.Services.Interfaces;
namespace DWH.ProjectServices.API.StartupExtensions;

public static class ProjectServicesExtensions
{
    public static IServiceCollection AddServices(this IServiceCollection services)
    {
        services.AddHttpClient();
        return services
            .AddTransient<IBaseProjectService, BaseProjectService>()
            .AddTransient<IProductGroupService, ProductGroupService>()
            .AddTransient<IProjectSubTypeService, ProjectSubTypeService>()
            .AddTransient<IBaseProjectPanelService, BaseProjectPanelService>()
            .AddTransient<IBaseProjectDataTypeService, BaseProjectDataTypeService>()
            .AddTransient<IBaseProjectPurposeService, BaseProjectPurposeService>()
            .AddTransient<IResetCorrectionTypeService, ResetCorrectionTypeService>()
            .AddTransient<IQCProjectService, QCProjectService>()
            .AddTransient<IOperationHelper, OperationHelper>()
            .AddTransient<ITokenService, TokenService>()
            .AddTransient<IQCPeriodService, QCPeriodService>()
            .AddSingleton<IRabbitMQSender, RabbitMQSender>()
            .AddSingleton<IRabbitMQConnectionFactory, RabbitMQConnectionFactory>()
            .AddTransient<IDateApiClient, DateApiClient>()
            .AddTransient<IAdministratorAPIClient, AdministratorAPIClient>()
            .AddTransient<IRetailerSeperationService, RetailerSeperationService>()
            .AddTransient<IPollyPolicyHelper, PollyPolicyHelper>()
            .AddTransient<IJiraApiClient, JiraAPIClient>()
            .AddTransient<IJiraHelper, JiraHelper>()
            .AddTransient<IQCSecurityAPIClient, QCSecurityApiClient>()
            .AddTransient<ISecurityHelper, SecurityHelper>()
            .AddTransient<IRoleHelper, RoleHelper>()
            .AddTransient<IRoleApiClient, RoleApiClient>()
            .AddSingleton<IMessageConsumer, MessageConsumer>()
            .AddTransient<IMessageSync, MessageSync>()
            .AddTransient<IErrorHandler, ErrorHandler>()
            .AddTransient<IProjectServicesHelper, ProjectServicesHelper>()
            .AddTransient<IProjectServicesApiClient, ProjectServicesApiClient>()
            .AddTransient<INotificationService, NotificationService>();
    }


    public static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        return services
            .AddTransient<IBaseProjectRepository, BaseProjectRepository>()
            .AddTransient<IProductGroupRepository, ProductGroupRepository>()
            .AddTransient<IProjectSubTypeRepository, ProjectSubTypeRepository>()
            .AddTransient<IBaseProjectPanelRepository, BaseProjectPanelRepository>()
            .AddTransient<IBaseProjectDataTypeRepository, BaseProjectDataTypeRepository>()
            .AddTransient<IBaseProjectPurposeRepository, BaseProjectPurposeRepository>()
            .AddTransient<IResetCorrectionTypeRepository, ResetCorrectionTypeRepository>()
            .AddTransient<IQCProjectRepository, QCProjectRepository>()
            .AddTransient<IQCPeriodRepository, QCPeriodRepository>()
            .AddTransient<IRetailerSeperationRepository, RetailerSeperationRepository>()
            .AddTransient<IOutBoxItemRepository, OutBoxItemRepository>();

    }
    public static IQueryable<TSource> WhereIfExists<TSource, TResult>(this IQueryable<TSource> source, Expression<Func<TSource, bool>> predicate, IEnumerable<TResult> collections)
    {
        if (collections == null || !collections.Any())
            return source;

        return source.Where(predicate);
    }

    public static bool NonCaseSensitiveContains(this string sourceText, string candidateText)
    {
        if (sourceText.ToUpper().Contains(candidateText.ToUpper()))
            return true;

        if (sourceText.Replace(" ", "").Equals(candidateText.Replace(" ", ""))) //just a space somewhere
            return true;

        return false;
    }

    public static IApplicationBuilder UseCountryFilter(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<CountryFilter>();
    }
}
