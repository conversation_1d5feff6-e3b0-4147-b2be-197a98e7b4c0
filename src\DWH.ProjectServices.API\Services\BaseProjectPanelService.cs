﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Services.Interfaces;

namespace DWH.ProjectServices.API.Services;

public class BaseProjectPanelService : IBaseProjectPanelService
{
    private readonly IBaseProjectPanelRepository _baseprojectPanelRepository;

    public BaseProjectPanelService(IBaseProjectPanelRepository baseProjectPanelRepository)
    {
        _baseprojectPanelRepository = baseProjectPanelRepository;
    }

    public async Task<IReadOnlyCollection<BaseProjectPanel>> GetAllAsync()
    {
        var result = await _baseprojectPanelRepository.GetAllAsync();

        return result;
    }
}
