﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using AutoMapper;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Entities;

[AutoMap(typeof(BaseProjectProductGroupModelDto))]
[Table("BaseProject_ProductGroup")]
public class BaseProjectProductGroupEntity
{
    [Key]
    public int Id { get; set; }
    [Column("BaseProjectId")]
    public int BaseProjectId { get; set; }

    public int ProductGroupId { get; set; }

    public bool? Deleted { get; set; }

}
