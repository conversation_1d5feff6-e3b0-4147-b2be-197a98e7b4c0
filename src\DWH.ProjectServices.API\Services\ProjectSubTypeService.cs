﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Services.Interfaces;

namespace DWH.ProjectServices.API.Services;

public class ProjectSubTypeService : IProjectSubTypeService
{
    private readonly IProjectSubTypeRepository _projectSubTypeRepository;

    public ProjectSubTypeService(IProjectSubTypeRepository projectSubTypeRepository) =>
                                               _projectSubTypeRepository = projectSubTypeRepository;



    public async Task<IReadOnlyCollection<ProjectSubTypes>> GetAllAsync()
    {
        var result = await _projectSubTypeRepository.FindAllAsync();
        return result;
    }

}
