﻿using System.Collections.Immutable;

namespace DWH.ProjectServices.API.Services.Constants;

public class RetailerRequestConstants
{
    public const int Pending = 1;
    public const int Executing = 2;
    public const int Finished = 3;
    public const int Edited = 4;
    public const int Declined = 5;
    public const int CSVAvailable = 6;

    public static readonly ImmutableDictionary<int, string> StatusNames =
        ImmutableDictionary.CreateRange(new Dictionary<int, string>
        {
            { Pending, "Pending" },
            { Executing, "Executing" },
            { Finished, "Finished" },
            { Edited, "Edited" },
            { Declined, "Declined" },
            { CSVAvailable, "CSV Available" }
        });
}