﻿using System.ComponentModel.DataAnnotations;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;

public class IRSeparationRequest
{
    [Required]
    public List<int> retailerSeperationRequestIds { get; set; }

    public IEnumerable<ValidationResult> Validate()
    {
        var results = new List<ValidationResult>();
        if (retailerSeperationRequestIds.Count <= 0)
        {
            results.Add(new ValidationResult("Must have atleast one retailerSeperationRequestId to proceed"));
        }

        return results;
    }
}
