using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using AutoMapper;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Constants;
using DWH.ProjectServices.API.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
namespace DWH.ProjectServices.API.Presentation.Controllers;

public class BaseProjectsController : ApiController
{
    private readonly IMapper _mapper;
    private readonly IBaseProjectService _baseProjectService;

    public BaseProjectsController(IMapper mapper, IBaseProjectService baseProjectService)
    {
        _mapper = mapper;
        _baseProjectService = baseProjectService;
    }

    /// <summary>
    /// Create a new BaseProject
    /// </summary>
    [HttpPost]
    [Consumes("application/json")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(typeof(BaseProjectResponse), StatusCodes.Status201Created)]
    public async Task<IActionResult> AddAsync(
        [Required][FromHeader(Name = "userName")] string userName,
        BaseProjectCreateRequest baseProjectCreateRequest)
    {
        baseProjectCreateRequest.CreatedBy = userName;
        baseProjectCreateRequest.UpdatedBy = userName;
        var baseProject = _mapper.Map<BaseProject>(baseProjectCreateRequest);

        var result = await _baseProjectService.AddAsync(baseProject);
        var resultResponse = _mapper.Map<BaseProjectResponse>(result);

        if (result != null)
        {
            return CreatedAtAction(nameof(GetBaseProjectAsync), new { baseProjectId = resultResponse.Id }, resultResponse);
        }

        return BadRequest(resultResponse);
    }


    /// <summary>
    /// Gets BaseProjects filtered by CountryId and PanelId
    /// </summary>
    [HttpPost("predecessors")]
    [Consumes("application/json")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(IReadOnlyList<BaseProjectPredecessorResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetAsync(BaseProjectPredecessorRequest bpPredecessorRequest)
    {

        if (bpPredecessorRequest.CountryId.IsPositive() && bpPredecessorRequest.PanelId.IsPositive())
        {

            var bpPredecessors = _mapper.Map<BaseProjectPredecessor>(bpPredecessorRequest);
            var baseProjects = await _baseProjectService.GetAllAsync(bpPredecessors);
            ICollection<BaseProjectPredecessorResponse> result = null;
            if (baseProjects != null)
            {
                result = _mapper.Map<ICollection<BaseProjectPredecessorResponse>>(baseProjects);
            }

            return OkOrEmpty(result);
        }
        return BadRequest("Invalid CountryId/PanelId is provided.");
    }


    /// <summary>
    /// Update a BaseProject 
    /// </summary>
    [HttpPut("{id}")]
    [Consumes("application/json")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(BaseProjectEditResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> UpdateAsync(
    [Required] [FromHeader(Name = "userName")]
    string userName, int id, BaseProjectEditRequest updateBaseProjectRequest)
    {
        try
        {
            updateBaseProjectRequest.UpdatedBy = userName;
            var editedbaseProject = _mapper.Map<BaseProject>(updateBaseProjectRequest);
            var response = await _baseProjectService.UpdateAsync(id, editedbaseProject);
            var result = _mapper.Map<BaseProjectEditResponse>(response);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            try
            {
                var issues = JsonSerializer.Deserialize<List<ProductGroupIssue>>(ex.Message);
                return BadRequest(new
                {
                    Message = "One or more product groups could not be updated.",
                    Issues = issues
                });
            }
            catch
            {
                return BadRequest(new { Message = ex.Message });
            }
        }
    }


    /// <summary>
    /// Gets a single BaseProject by Id
    /// </summary>
    [HttpGet("baseProject/{baseProjectId}")]
    [Consumes("application/json")]
    [ActionName(nameof(GetBaseProjectAsync))]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(BaseProjectResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetBaseProjectAsync(int baseProjectId)
    {
        var baseProject = await _baseProjectService.GetAsync(baseProjectId);
        var result = _mapper.Map<BaseProjectGetResponse>(baseProject);


        return OkOrEmpty(result);
    }


    /// <summary>
    /// Gets all BaseProjects filtered or unfiltered
    /// </summary>
    [HttpPost("list")]
    [Consumes("application/json")]
    [EndpointSummary("Gets all baseprojects filtered or unfiltered")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(IReadOnlyList<BaseProjectListResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetAsyncList([FromBody] BaseProjectListRequest baseProjectListRequest)
    {
        var baseProjectList = _mapper.Map<BaseProjectsLists>(baseProjectListRequest);
        var result = await _baseProjectService.GetAsyncList(baseProjectList);
        var response = _mapper.Map<BaseProjectListResponse>(result);
        return OkOrEmpty(response);
    }

    private BaseProjectListRequest FilterBaseProjectListRequest(BaseProjectListRequest baseProjectListRequest, string countryIds)
    {
        BaseProjectListRequest filteredRequest = HttpContext.Items["FilteredBaseProjectListRequest"] as BaseProjectListRequest;

        var countryIdArray = countryIds.Split(',').Select(int.Parse).ToArray();

        if (baseProjectListRequest.CountryIds == null || baseProjectListRequest.CountryIds.Length == 0)
        {
            filteredRequest.CountryIds = countryIdArray;
        }
        else if (filteredRequest.CountryIds.Length == 0)
        {
            filteredRequest.CountryIds = new int[] { -1 }; // No matching country IDs from headers
        }

        return filteredRequest;
    }



    /// <summary>
    /// Delete a BaseProject
    /// </summary>
    [HttpDelete]
    [Consumes("application/json")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    //[ProducesResponseType(typeof(DependencyResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> DeleteAsync([Required][FromHeader(Name = "userName")] string userName, BaseProjectDeleteRequest deleteBaseProject)
    {
        deleteBaseProject.DeletedBy = userName;
        var deleteBaseProjectReq = _mapper.Map<BaseProjectDeletes>(deleteBaseProject);
        IReadOnlyList<Dependencies> dependencies = await _baseProjectService.DeleteAsync(deleteBaseProjectReq);
        var successfulResponses = dependencies?.Where(item => item.StatusCode == StatusCodes.Status200OK)?.ToList();
        var responses = _mapper.Map<IReadOnlyList<DependencyResponse>>(dependencies);

        return StatusCode(StatusCodes.Status207MultiStatus, responses);
    }

    /// <summary>
    /// Update specific fields of BaseProjects in bulk
    /// </summary>
    [HttpPut("bulk")]
    [Consumes("application/json")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(IReadOnlyList<BaseProjectEditResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> UpdateBulkBpAsync(
    [Required] [FromHeader(Name = "userName")]
        string userName, BulkBPEditRequest updateBaseProjectBulkRequest)
    {
        if (updateBaseProjectBulkRequest.BaseProjectIds.Count > 100)
        {
            return BadRequest("Found more than 100 baseproject ids in the request");
        }

        updateBaseProjectBulkRequest.UpdatedBy = userName;

        var bulkEditedbaseProject = _mapper.Map<BaseProjectBulkEdit>(updateBaseProjectBulkRequest);

        var result = await _baseProjectService.UpdateBulkAsync(bulkEditedbaseProject);
        var response = _mapper.Map<IReadOnlyList<BaseProjectEditResponse>>(result);

        return Ok(response);

    }

    /// <summary>
    /// Gets all BaseProjects filtered by TypeId, StatusId, and CountryId
    /// </summary>
    [HttpGet("list")]
    [Consumes("application/json")]
    [EndpointSummary("Gets all baseprojects filtered by TypeId, StatusId, and CountryId")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(IReadOnlyList<BaseProjectNameandIdListResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetAsyncListBaseProjects(
        [FromHeader(Name = "Custom-Countryid")] string countryIds,
        [FromQuery] int typeId)
    {
        var baseProjectListRequest = new BaseProjectNameAndIdListRequest
        {
            TypeId = typeId,
            StatusId = typeId == (int)BaseProjectTypeConstants.Industry_Retailer ? (int)RetailerRequestConstants.Pending : 0,
        };

        if (!string.IsNullOrEmpty(countryIds))
        {
            baseProjectListRequest.CountryIds = countryIds
                .Split(',')
                .Select(int.Parse)
                .ToArray();
        }

        var baseProjectList = _mapper.Map<BaseProjectNameandIdLists>(baseProjectListRequest);
        var result = await _baseProjectService.GetAsyncListBaseProjects(baseProjectList);
        var response = _mapper.Map<BaseProjectNameandIdListResponse>(result);
        return OkOrEmpty(response);
    }

    /// <summary>
    /// Return Authorized BaseProjectIds
    /// </summary>
    [HttpPost("baseproject")]
    [Consumes("application/json")]
    [ActionName(nameof(GetBaseProjectAsync))]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(List<int>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetBaseProjectAsync(BaseProjectCountryRequest baseProjectCountryRequest)
    {
        var baseProjectCountryModel = _mapper.Map<BaseProjectCountries>(baseProjectCountryRequest);
        var result = await _baseProjectService.GetAsync(baseProjectCountryModel);
        if (result.Count == 0)
        {
            return StatusCode(StatusCodes.Status403Forbidden);
        }
        return OkOrEmptyList(result);
    }

    /// <summary>
    /// Gets all BaseProjects filtered or unfiltered
    /// </summary>
    [HttpGet("userlist")]
    [Consumes("application/json")]
    [EndpointSummary("Gets all baseproject users")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(IReadOnlyList<string>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetUsersList()
    {
        var result = await _baseProjectService.GetUsersList();
        return OkOrEmpty(result);
    }

    /// <summary/>
    /// Return BaseProject Associations
    [HttpGet("baseProjectAssociations/{baseProjectId}")]
    [Consumes("application/json")]
    [ActionName(nameof(GetBaseProjectAssociationsAsync))]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(List<int>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetBaseProjectAssociationsAsync(int baseProjectId)
    {
        var result = await _baseProjectService.FetchBaseProjectAssociations(baseProjectId);
        return OkOrEmpty(result);
    }


    /// <summary>
    /// Create multiple BaseProjects from existing BaseProject IDs
    /// </summary>
    [HttpPost("bulk")]
    [Consumes("application/json")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(typeof(IReadOnlyList<BulkAddAsyncResponse>), StatusCodes.Status207MultiStatus)]
    public async Task<IActionResult> AddBulkBpAsync(
       [Required][FromHeader(Name = "userName")] string userName,
       [FromHeader(Name = "Custom-Countryid")] string countryIds,
       BulkBPCreateRequest baseProjectCreateRequests)
    {
        var result = await _baseProjectService.AddBulkAsync(baseProjectCreateRequests.BaseProjectIds, userName, countryIds);
        var response = _mapper.Map<IReadOnlyList<BulkAddAsyncResponse>>(result);

        return StatusCode(StatusCodes.Status207MultiStatus, response);
    }

}

