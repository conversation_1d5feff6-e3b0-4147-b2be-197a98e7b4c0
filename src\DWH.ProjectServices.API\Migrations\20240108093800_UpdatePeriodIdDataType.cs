﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DWH.ProjectServices.API.Migrations;

/// <inheritdoc />
public partial class UpdatePeriodIdDataType : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AlterColumn<long>(
            name: "StockPeriodId",
            table: "StockInitialization",
            type: "bigint",
            nullable: true,
            oldClrType: typeof(int),
            oldType: "integer",
            oldNullable: true);

        migrationBuilder.AlterColumn<DateTimeOffset>(
            name: "<PERSON><PERSON>hen",
            table: "QCPeriod",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(2024, 1, 8, 9, 38, 0, 41, DateTimeKind.Unspecified).AddTicks(5002), new TimeSpan(0, 0, 0, 0, 0)),
            oldClrType: typeof(DateTimeOffset),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTimeOffset(new DateTime(2024, 1, 1, 10, 33, 47, 300, DateTimeKind.Unspecified).AddTicks(166), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AlterColumn<long>(
            name: "RefPeriodId",
            table: "Period",
            type: "bigint",
            nullable: true,
            oldClrType: typeof(int),
            oldType: "integer",
            oldNullable: true);

        migrationBuilder.AlterColumn<DateTime>(
            name: "CreatedWhen",
            table: "BaseProjects",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTime(2024, 1, 8, 9, 38, 0, 41, DateTimeKind.Utc).AddTicks(4076),
            oldClrType: typeof(DateTime),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTime(2024, 1, 1, 10, 33, 47, 299, DateTimeKind.Utc).AddTicks(9500));
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AlterColumn<int>(
            name: "StockPeriodId",
            table: "StockInitialization",
            type: "integer",
            nullable: true,
            oldClrType: typeof(long),
            oldType: "bigint",
            oldNullable: true);

        migrationBuilder.AlterColumn<DateTimeOffset>(
            name: "CreatedWhen",
            table: "QCPeriod",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(2024, 1, 1, 10, 33, 47, 300, DateTimeKind.Unspecified).AddTicks(166), new TimeSpan(0, 0, 0, 0, 0)),
            oldClrType: typeof(DateTimeOffset),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTimeOffset(new DateTime(2024, 1, 8, 9, 38, 0, 41, DateTimeKind.Unspecified).AddTicks(5002), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AlterColumn<int>(
            name: "RefPeriodId",
            table: "Period",
            type: "integer",
            nullable: true,
            oldClrType: typeof(long),
            oldType: "bigint",
            oldNullable: true);

        migrationBuilder.AlterColumn<DateTime>(
            name: "CreatedWhen",
            table: "BaseProjects",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTime(2024, 1, 1, 10, 33, 47, 299, DateTimeKind.Utc).AddTicks(9500),
            oldClrType: typeof(DateTime),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTime(2024, 1, 8, 9, 38, 0, 41, DateTimeKind.Utc).AddTicks(4076));
    }
}
