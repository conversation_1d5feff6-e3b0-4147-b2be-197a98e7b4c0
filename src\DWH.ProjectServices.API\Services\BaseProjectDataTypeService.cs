﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Services.Interfaces;

namespace DWH.ProjectServices.API.Services;

public class BaseProjectDataTypeService : IBaseProjectDataTypeService
{
    private readonly IBaseProjectDataTypeRepository _baseprojectDataTypeRepository;
    public BaseProjectDataTypeService(IBaseProjectDataTypeRepository baseprojectDataTypeRepository)
    {
        _baseprojectDataTypeRepository = baseprojectDataTypeRepository;
    }

    public async Task<IReadOnlyCollection<DataTypes>> GetAllAsync()
    {
        var result = await _baseprojectDataTypeRepository.GetAllAsync();

        return result;
    }
}
