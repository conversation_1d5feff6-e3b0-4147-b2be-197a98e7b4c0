﻿using System.Net;
using AutoMapper;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Constants;
using DWH.ProjectServices.API.StartupExtensions;
using Microsoft.EntityFrameworkCore;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories;

public class RetailerSeperationRepository : IRetailerSeperationRepository
{
    private readonly PostgreSqlDbContext _postdbContext;
    private readonly IMapper _mapper;
    private IBaseProjectRepository _baseProjectRepository;
    private readonly OracleDbContext _oracledbContext;
    private IQCProjectRepository _qcProjectRepository;
    private readonly IServiceScopeFactory _serviceScopeFactory;

    public RetailerSeperationRepository(PostgreSqlDbContext postdbContext, OracleDbContext oracleDbContext, IMapper mapper, IServiceScopeFactory serviceScopeFactory)
    {
        _postdbContext = postdbContext;
        _mapper = mapper;
        _serviceScopeFactory = serviceScopeFactory;
        _oracledbContext = oracleDbContext;
        _baseProjectRepository = new BaseProjectRepository
             (_oracledbContext, _postdbContext, _mapper, serviceScopeFactory);
        _qcProjectRepository = new QCProjectRepository
             (_postdbContext, _mapper, serviceScopeFactory);
    }
    public async Task<RetailerSeperationRequest> AddAsync(RetailerSeperationRequest RetailerSeperation)
    {
        var newRetailerSeperationRequest = _mapper.Map<RetailerSeperationRequestEntity>(RetailerSeperation);
        foreach (var detail in newRetailerSeperationRequest.RetailerSeperationRequestDetails)
        {
            detail.UpdatedWhen = DateTimeOffset.UtcNow;
        }
        _postdbContext.RetailerSeperationRequests.Add(newRetailerSeperationRequest);
        await _postdbContext.SaveChangesAsync();
        return _mapper.Map<RetailerSeperationRequest>(newRetailerSeperationRequest);
    }


    public async Task<List<int>> GetBaseProjectsbyRequestIdAsync(int retailerSeperationRequestId)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

        var sourceBpIds = await dbContext.RetailerSeperation
                           .Where(rs => rs.RetailerSeperationRequestId == retailerSeperationRequestId)
                           .Select(rs => rs.SourceBPId)
                           .ToListAsync();


        if (!sourceBpIds.Any())
        {
            throw new EntityNotExistsException($"No SourceBPIds found for RetailerSeperationRequestId {retailerSeperationRequestId}");
        }

        return sourceBpIds;
    }


    public async Task AddRetailerBPIdAsync(int sourceBPId, int retailerSeperationRequestId, int newRetailerBPId)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

        var retailerSeperation = await dbContext.RetailerSeperation
            .FirstOrDefaultAsync(rs => rs.SourceBPId == sourceBPId && rs.RetailerSeperationRequestId == retailerSeperationRequestId);
        if (retailerSeperation == null)
        {
            throw new EntityNotExistsException($"No RetailerSeperation found with SourceBPId {sourceBPId} and RetailerSeperationRequestId {retailerSeperationRequestId}");
        }
        retailerSeperation.RetailerBPId = newRetailerBPId;
        await dbContext.SaveChangesAsync();
    }

    public async Task AddDetailAndUpdateStatusAsync(RetailerSeperationRequestDetail details)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

        var retailerSeperationRequestDetail = _mapper.Map<RetailerSeperationRequestDetailEntity>(details);
        dbContext.RetailerSeperationRequestDetail.Add(retailerSeperationRequestDetail);
        var retailerSeperationRequest = await dbContext.RetailerSeperationRequests
           .FirstOrDefaultAsync(rs => rs.Id == details.RetailerSeperationRequestId);

        if (retailerSeperationRequest == null)
        {
            throw new EntityNotExistsException($"No RetailerSeperationRequest found with ID {details.RetailerSeperationRequestId}");
        }
        retailerSeperationRequest.RequestStatusId = (int)RetailerRequestConstants.Executing;

        await dbContext.SaveChangesAsync();
    }

    public async Task<IReadOnlyList<RetailerSeperationRequest>> GetAsyncList(RetailerSeperationsLists retailerSeperations)
    {
        var result = await _postdbContext.RetailerSeperationRequests
            .Include(b => b.RetailerSeperations)
            .Include(b => b.RetailerSeperationRequestDetails)
            .WhereIfExists(i => retailerSeperations.Ids.ToList().Contains(i.Id), retailerSeperations.Ids)
            .WhereIfExists(i => retailerSeperations.FromPeriodIds.ToList().Contains(i.FromPeriodId), retailerSeperations.FromPeriodIds)
            .WhereIfExists(i => retailerSeperations.ToPeriodIds.ToList().Contains(i.ToPeriodId), retailerSeperations.ToPeriodIds)
            .WhereIfExists(i => retailerSeperations.ResetCorrections.ToList().Contains(i.ResetCorrection), retailerSeperations.ResetCorrections)
            .WhereIfExists(i => retailerSeperations.Extrapolations.ToList().Contains(i.Extrapolation), retailerSeperations.Extrapolations)
            .WhereIfExists(i => i.JiraId.Contains(retailerSeperations.JiraIds), retailerSeperations.JiraIds)
            .WhereIfExists(i => (retailerSeperations.RequestStatusIds.ToList().Contains(6) && i.RetailerSeperations.Any(pg => pg.IsError == true)) || retailerSeperations.RequestStatusIds.ToList().Contains(i.RequestStatusId), retailerSeperations.RequestStatusIds)
            .WhereIfExists(i => i.RetailerSeperations.Any(pg => retailerSeperations.SourceBPIds.Contains(pg.SourceBPId)), retailerSeperations.SourceBPIds)
            .WhereIfExists(i => i.RetailerSeperations.Any(pg => retailerSeperations.RetailerBPIds.Contains(pg.RetailerBPId)), retailerSeperations.RetailerBPIds)
            .WhereIfExists(i => i.RetailerSeperationRequestDetails.Any(pg => retailerSeperations.Usernames.Contains(pg.UpdatedBy)), retailerSeperations.Usernames)
            .Where(i => retailerSeperations.StartDate == null || i.RetailerSeperationRequestDetails.Any(pg => retailerSeperations.StartDate <= pg.UpdatedWhen))
            .Where(i => retailerSeperations.EndDate == null || i.RetailerSeperationRequestDetails.Any(pg => retailerSeperations.EndDate > pg.UpdatedWhen))
            .Where(i => i.RequestStatusId != 5)
            .OrderByDescending(i => i.Id) // Order by descending based on Id
            .ToListAsync();

        if (!result.Any())
            throw new EntityNotExistsException("No retailer separations found", "RetailerSeperations");

        return _mapper.Map<IReadOnlyList<RetailerSeperationRequest>>(result);
    }

    public async Task<IReadOnlyList<string>> GetUsersList()
    {
        var result = await _postdbContext.RetailerSeperationRequests
            .Include(b => b.RetailerSeperationRequestDetails)
            .SelectMany(b => b.RetailerSeperationRequestDetails.Select(d => d.UpdatedBy))
            .Distinct()
            .ToListAsync();

        if (!result.Any())
            throw new EntityNotExistsException("No retailer separation users found", "RetailerSeperations");

        return result;
    }

    public async Task<RetailerSeperationRequest> GetAsync(int requestId)
    {
        var retailerSeparation = await _postdbContext
                        .RetailerSeperationRequests
                        .Include(b => b.RetailerSeperationRequestDetails)
                        .Include(b => b.RetailerSeperations)
        .SingleOrDefaultAsync(b => b.Id == requestId);

        if (retailerSeparation is null)
            throw new EntityNotExistsException($"No Retailer Separation Request exists with Request Id {requestId}");

        var retailerSeparationGetRequest = new RetailerSeperationRequest
        {
            Id = requestId,
            FromPeriodId = retailerSeparation.FromPeriodId,
            ToPeriodId = retailerSeparation.ToPeriodId,
            Extrapolation = retailerSeparation.Extrapolation,
            ResetCorrection = retailerSeparation.ResetCorrection,
            JiraId = retailerSeparation.JiraId,
            RequestStatusId = retailerSeparation.RequestStatusId,
            RetailerSeperations = retailerSeparation.RetailerSeperations.Select(rs => new RetailerSeperation
            {
                Id = rs.Id,
                SourceBPId = rs.SourceBPId,
                SourceBPName = _postdbContext.BaseProjects.Where(bp => bp.Id == rs.SourceBPId).Select(bp => bp.Name)?.FirstOrDefault() ?? string.Empty,
                RetailerBPId = rs.RetailerBPId,
                RetailerBPName = rs.RetailerBPId == 0 ? string.Empty : _postdbContext.BaseProjects.Where(bp => bp.Id == rs.RetailerBPId).Select(bp => bp.Name)?.FirstOrDefault() ?? string.Empty,
                RetailerSeperationRequestId = rs.RetailerSeperationRequestId,
                IsError = rs.IsError,
            }).ToList(),
            RetailerSeperationRequestDetails = retailerSeparation.RetailerSeperationRequestDetails.Select(rd => new RetailerSeperationRequestDetail
            {
                Id = rd.Id,
                RetailerSeperationRequestId = rd.RetailerSeperationRequestId,
                UpdatedBy = rd.UpdatedBy,
                UpdatedWhen = rd.UpdatedWhen,
                RequestStatusId = rd.RequestStatusId,
                RequestStatus = RetailerRequestConstants.StatusNames.ContainsKey(rd.RequestStatusId)
                        ? RetailerRequestConstants.StatusNames[rd.RequestStatusId]
                        : "Unknown"
            }).ToList()
        };
        return retailerSeparationGetRequest;
    }

    public async Task<string> GetJiraId(int requestId)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

        var retailerSeparation = await dbContext
                        .RetailerSeperationRequests
                        .Include(b => b.RetailerSeperationRequestDetails)
                        .Include(b => b.RetailerSeperations)
        .SingleOrDefaultAsync(b => b.Id == requestId);

        if (retailerSeparation is null)
            throw new EntityNotExistsException($"No Retailer Separation Request exists with Request Id {requestId}");

        return retailerSeparation.JiraId;
    }

    public async Task<IReadOnlyList<int>> GetAuthorizedBaseProjectsByCountry(RetailerSeparationCountries retailerseparationCountryRequest)
    {
        var baseProject = await _postdbContext.BaseProjects
           .Where(b => retailerseparationCountryRequest.CountryIds.Contains(b.CountryId)
               && retailerseparationCountryRequest.RetailerSeperationIds.Contains(b.Id)
               && b.Deleted == false)
                .Select(p => p.Id)
           .Distinct()
            .ToListAsync();
        return baseProject;
    }

    public async Task<IReadOnlyList<int>> GetAuthorizedRetailerSeperationsByCountry(RetailerSeparationCountries rsCountryRequest)
    {
        List<int> retailerSeperationids = new List<int>();
        foreach (var retailerSeperationId in rsCountryRequest.RetailerSeperationIds)
        {
            var sourcebp = await _postdbContext.RetailerSeperation
             .Where(rs => rs.Id == retailerSeperationId)
             .Select(rs => rs.SourceBPId)
             .SingleOrDefaultAsync();

            int? baseProject = await _postdbContext.BaseProjects
                            .Where(b => rsCountryRequest.CountryIds.Contains(b.CountryId)
                                        && sourcebp == b.Id
                                        && b.Deleted == false)
                            .Select(p => p.Id)
                            .SingleOrDefaultAsync();
            if (baseProject != null)
            {
                retailerSeperationids.Add(retailerSeperationId);
            }
        }
        return retailerSeperationids;
    }

    public async Task<RetailerSeperationRequest> UpdateAsync(string username, RetailerSeperationRequest retailerseparationEditRequest)
    {
        bool isErrorCase = retailerseparationEditRequest.ToPeriodId == -1;

        var retailerseparationRequest = await _postdbContext
                                .RetailerSeperationRequests
                                .Where(r => r.Id == retailerseparationEditRequest.Id)
                                .Include(b => b.RetailerSeperations)
                                .Include(d => d.RetailerSeperationRequestDetails)
                                .SingleOrDefaultAsync();

        if (retailerseparationRequest is null)
            throw new EntityNotExistsException($"No Retailer Separation Request exists with Request Id {retailerseparationEditRequest.Id}");

        if (!isErrorCase)
        {
            retailerseparationRequest.FromPeriodId = retailerseparationEditRequest.FromPeriodId;
            retailerseparationRequest.ToPeriodId = retailerseparationEditRequest.ToPeriodId;
            retailerseparationRequest.ResetCorrection = retailerseparationEditRequest.ResetCorrection;
            retailerseparationRequest.Extrapolation = retailerseparationEditRequest.Extrapolation;
        }
        else
        {
            var resultInfo = retailerseparationEditRequest
                   .RetailerSeperations.SingleOrDefault();

            if (resultInfo != null)
            {
                foreach (var retailerSeparationInfo in retailerseparationRequest.RetailerSeperations)
                {
                    if (resultInfo.SourceBPId == retailerSeparationInfo.SourceBPId)
                    {
                        retailerSeparationInfo.IsError = resultInfo.IsError;
                    }
                }
            }
        }

        UpdateRequestDetail(retailerseparationRequest, retailerseparationEditRequest.Id, RetailerRequestConstants.Edited, username);

        await _postdbContext.SaveChangesAsync();

        return _mapper.Map<RetailerSeperationRequest>(retailerseparationEditRequest);
    }

    public async Task<bool> UpdateStatusAsync(RetailerSeparationStatusDetails retailerseparationStatusDetails)
    {
        var retailerseparationRequest = await _postdbContext
                                .RetailerSeperationRequests
                                .Where(r => r.Id == retailerseparationStatusDetails.RequestId)
                                .FirstOrDefaultAsync();

        if (retailerseparationRequest is null)
            return false;

        retailerseparationRequest.RequestStatusId = retailerseparationStatusDetails.StatusId;
        UpdateRequestDetail(retailerseparationRequest, retailerseparationStatusDetails.RequestId, retailerseparationStatusDetails.StatusId, retailerseparationStatusDetails.Username);

        await _postdbContext.SaveChangesAsync();

        return true;
    }

    private void UpdateRequestDetail(RetailerSeperationRequestEntity retailerseparationRequest, int requestId, int statusId, string username)
    {
        var requestDetail = retailerseparationRequest
                                .RetailerSeperationRequestDetails
                                .SingleOrDefault(d => d.RequestStatusId == statusId
                                && d.RetailerSeperationRequestId == requestId);

        if (requestDetail == null)
        {
            var newRetailerSeperationRequestDetail = new RetailerSeperationRequestDetailEntity
            {
                RetailerSeperationRequestId = requestId,
                UpdatedBy = username,
                UpdatedWhen = DateTimeOffset.UtcNow,
                RequestStatusId = statusId
            };
            _postdbContext.RetailerSeperationRequestDetail.Add(newRetailerSeperationRequestDetail);
        }
        else
        {
            requestDetail.UpdatedBy = username;
            requestDetail.UpdatedWhen = DateTimeOffset.UtcNow;
        }
    }

    public async Task<IReadOnlyList<ResponseInfoRetailerSeperation>> DeleteSourceBPAsync(IRSeperationDeletes IRSeperationDeleteRequest)
    {
        var responses = new List<ResponseInfoRetailerSeperation>();

        foreach (var retailerSeperationId in IRSeperationDeleteRequest.RetailerSeperationIds)
        {
            var retailerSeperation = await _postdbContext.RetailerSeperation
                .FirstOrDefaultAsync(qcp => qcp.Id == retailerSeperationId);

            if (retailerSeperation != null)
            {
                _postdbContext.RetailerSeperation.Remove(retailerSeperation);

                responses.Add(
                    new ResponseInfoRetailerSeperation(
                        retailerSeperation.SourceBPId.ToString(),
                        (int)HttpStatusCode.OK,
                        HttpStatusCode.OK.ToString(),
                        retailerSeperation.RetailerSeperationRequestId
                    )
                );
            }
            else
            {
                responses.Add(
                    new ResponseInfoRetailerSeperation(
                        null,
                        (int)HttpStatusCode.NotFound,
                        HttpStatusCode.NotFound.ToString(),
                        null
                    )
                );
            }
        }

        try
        {
            await _postdbContext.SaveChangesAsync();
        }
        catch (Exception)
        {
            responses.Add(
                new ResponseInfoRetailerSeperation(
                    null,
                    (int)HttpStatusCode.InternalServerError,
                    HttpStatusCode.InternalServerError.ToString(),
                    null
                )
            );
        }

        return responses;
    }

    public async Task<BaseProject> PerformRetailerSeparation(int sourceBPId, int typeId, string username, int retailerSeperationRequestId)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

        var updatedProject = await _baseProjectRepository.UpdateTypeIdAsync(sourceBPId, typeId, username, dbContext);

        var retailerBP = _baseProjectRepository.CreateRetailerBaseProject(updatedProject, username);

        if (retailerBP == null)
        {
            throw new ArgumentNullException("Failed to Create Retailer BaseProject.");
        }

        dbContext.BaseProjects.Add(retailerBP);

        await dbContext.SaveChangesAsync();
        foreach (var QCPeriods in retailerBP.QCProjects.QCPeriods)
        {
            foreach (var Periods in QCPeriods.Periods)
            {
                Periods.RefProjectId = retailerBP.Id;
            }
        }
        await dbContext.SaveChangesAsync();
        await AddRetailerBPIdAsync(sourceBPId, retailerSeperationRequestId, retailerBP.Id);
        await UpdateIndustryQCProjectAsync(updatedProject, username);
        return _mapper.Map<BaseProject>(retailerBP);
    }
    public async Task<int> GetQCProjectIdAsync(int sourceBPId)
    {
        var sourceBaseProject = await _baseProjectRepository.GetAsync(sourceBPId);

        if (sourceBaseProject == null)

            throw new EntityNotExistsException($"BaseProject with ID {sourceBPId} not found.");

        return sourceBaseProject.QCProjects.Id;

    }

    private async Task UpdateIndustryQCProjectAsync(BaseProjectEntity baseProject, string username)
    {
        QCProjectUpdates qcProjectIndustry = new QCProjectUpdates
        {
            IsAutoLoad = false,
            ResetCorrectionTypeId = 1,
            SQCMode = baseProject.QCProjects.SQCMode,
            IsAutomatedPriceCheck = baseProject.QCProjects.IsAutomatedPriceCheck,
            UpdatedBy = username
        };
        var result = await _qcProjectRepository.UpdateAsync(baseProject.QCProjects.Id, qcProjectIndustry);
    }

    public async Task<List<int>> GetRetailerSeperationRequestIdsBySourceBpIdAsync(int sourceBpId)
    {
        var retailerSeperationRequestIds = await _postdbContext.RetailerSeperation
            .Join(
                _postdbContext.RetailerSeperationRequests,
                rs => rs.RetailerSeperationRequestId,
                rsr => rsr.Id,
                (rs, rsr) => new { rs, rsr })
            .Where(joined => joined.rs.SourceBPId == sourceBpId && joined.rsr.RequestStatusId == (int)RetailerRequestConstants.Pending)
            .Select(joined => joined.rs.RetailerSeperationRequestId)
            .Distinct()
            .ToListAsync();
        return retailerSeperationRequestIds;
    }

    public async Task UpdateRetailerSeparationRequestStatus(int retailerRequestId)
    {
        var retailerSeperationRequest = await _postdbContext
                                .RetailerSeperationRequests
                                .FirstOrDefaultAsync(b => b.Id == retailerRequestId);

        if (retailerSeperationRequest == null)
        {
            throw new EntityNotExistsException($"No Retailer Separation Request exists with Id {retailerRequestId}",
                                                "RetailerSeperationRequest", retailerRequestId);
        }

        retailerSeperationRequest.RequestStatusId = (int)RetailerRequestConstants.CSVAvailable;
        await _postdbContext.SaveChangesAsync();
    }
    public async Task<bool> CheckRetailerSeparationState(int baseProjectsId)
    {
        var baseProject = await _baseProjectRepository.GetAsync(baseProjectsId);
        if (baseProject.TypeId != (int)ProjectSubType.IndustryRetailer)
            return false;

        var baseProjectInPendingState = await GetRetailerSeperationRequestIdsBySourceBpIdAsync(baseProjectsId);

        if (baseProjectInPendingState.Count > 0)
            return true;

        return false;

    }

}
