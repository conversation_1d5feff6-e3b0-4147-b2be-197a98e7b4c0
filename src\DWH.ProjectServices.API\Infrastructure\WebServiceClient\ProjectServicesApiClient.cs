﻿using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Constants;
using DWH.ProjectServices.API.Services.Helper.Interface;

namespace DWH.ProjectServices.API.Infrastructure.WebServiceClient;

public class ProjectServicesApiClient : BaseApiClient, IProjectServicesApiClient
{
    public ProjectServicesApiClient(
        IHttpClientFactory httpClientFactory,
        ITokenService tokenService,
        ILogger<ProjectServicesApiClient> logger,
        IPollyPolicyHelper pollyHelper)
        : base(httpClientFactory, tokenService, logger, pollyHelper)
    {
    }

    public async Task<ServiceResponse<T>> PostAsync<T>(string requestUri, StringContent requestContent, string username)
    {
        var client = await GetHttpClientAsync("ProjectServicesAPI", AppConstants.BuilderAPI);
        client.DefaultRequestHeaders.Add("username", username);
        return await ExecuteWithPoliciesAsync<T>(() => client.PostAsync(requestUri, requestContent), requestUri);
    }
}
