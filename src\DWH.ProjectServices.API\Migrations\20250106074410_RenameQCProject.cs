﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DWH.ProjectServices.API.Migrations;

/// <inheritdoc />
public partial class RenameQCProject : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_QCPeriod_QCProjects_QCProjectId",
            table: "QCPeriod");

        migrationBuilder.DropIndex(
            name: "IX_QCProjects_BaseProjectId",
            table: "QCProjects");

        migrationBuilder.DropIndex(
            name: "IX_QCProjects_ResetCorrectionTypeId",
            table: "QCProjects");

        migrationBuilder.RenameTable(name: "QCProjects", newName: "QCProject");

        migrationBuilder.CreateIndex(
            name: "IX_QCProject_BaseProjectId",
            table: "QCProject",
            column: "BaseProjectId",
            unique: true);

        migrationBuilder.CreateIndex(
            name: "IX_QCProject_ResetCorrectionTypeId",
            table: "QCProject",
            column: "ResetCorrectionTypeId");

        migrationBuilder.AddForeignKey(
            name: "FK_QCPeriod_QCProject_QCProjectId",
            table: "QCPeriod",
            column: "QCProjectId",
            principalTable: "QCProject",
            principalColumn: "Id");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropForeignKey(
            name: "FK_BaseProject_Predecessors_BaseProject_BaseProjectId",
            table: "BaseProject_Predecessors");

        migrationBuilder.DropForeignKey(
            name: "FK_BaseProject_ProductGroups_BaseProject_BaseProjectId",
            table: "BaseProject_ProductGroups");

        migrationBuilder.DropForeignKey(
            name: "FK_QCPeriod_QCProject_QCProjectEntityId",
            table: "QCPeriod");

        migrationBuilder.DropTable(
            name: "QCProject");

        migrationBuilder.DropIndex(
            name: "IX_QCPeriod_QCProjectEntityId",
            table: "QCPeriod");

        migrationBuilder.DropColumn(
            name: "QCProjectEntityId",
            table: "QCPeriod");

        migrationBuilder.AlterColumn<DateTimeOffset>(
            name: "CreatedWhen",
            table: "QCPeriod",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(2025, 1, 6, 3, 41, 30, 191, DateTimeKind.Unspecified).AddTicks(1163), new TimeSpan(0, 0, 0, 0, 0)),
            oldClrType: typeof(DateTimeOffset),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTimeOffset(new DateTime(2025, 1, 6, 7, 44, 9, 554, DateTimeKind.Unspecified).AddTicks(3174), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AlterColumn<DateTimeOffset>(
            name: "CreatedWhen",
            table: "BaseProject",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(2025, 1, 6, 3, 41, 30, 191, DateTimeKind.Unspecified).AddTicks(54), new TimeSpan(0, 0, 0, 0, 0)),
            oldClrType: typeof(DateTimeOffset),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTimeOffset(new DateTime(2025, 1, 6, 7, 44, 9, 554, DateTimeKind.Unspecified).AddTicks(2246), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AddColumn<int>(
            name: "QCProjectsId",
            table: "BaseProject",
            type: "integer",
            nullable: true);

        migrationBuilder.CreateTable(
            name: "QCProjects",
            columns: table => new
            {
                Id = table.Column<int>(type: "integer", nullable: false)
                    .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                BaseProjectId = table.Column<int>(type: "integer", nullable: false),
                IsAutoLoad = table.Column<bool>(type: "boolean", nullable: true),
                IsAutomatedPriceCheck = table.Column<bool>(type: "boolean", nullable: true),
                ResetCorrectionTypeId = table.Column<int>(type: "integer", nullable: true),
                SQCMode = table.Column<int>(type: "integer", nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_QCProjects", x => x.Id);
                table.ForeignKey(
                    name: "FK_QCProjects_ResetCorrectionTypes_ResetCorrectionTypeId",
                    column: x => x.ResetCorrectionTypeId,
                    principalTable: "ResetCorrectionTypes",
                    principalColumn: "Id");
            });

        migrationBuilder.CreateIndex(
            name: "IX_QCPeriod_QCProjectId",
            table: "QCPeriod",
            column: "QCProjectId");

        migrationBuilder.CreateIndex(
            name: "IX_QCProjects_BaseProjectId",
            table: "QCProjects",
            column: "BaseProjectId");

        migrationBuilder.CreateIndex(
            name: "IX_QCProjects_ResetCorrectionTypeId",
            table: "QCProjects",
            column: "ResetCorrectionTypeId");

        migrationBuilder.AddForeignKey(
            name: "FK_BaseProject_QCProjects_QCProjectsId",
            table: "BaseProject",
            column: "QCProjectsId",
            principalTable: "QCProjects",
            principalColumn: "Id");

        migrationBuilder.AddForeignKey(
            name: "FK_BaseProject_Predecessors_BaseProject_BaseProjectId",
            table: "BaseProject_Predecessors",
            column: "BaseProjectId",
            principalTable: "BaseProject",
            principalColumn: "Id");

        migrationBuilder.AddForeignKey(
            name: "FK_BaseProject_ProductGroups_BaseProject_BaseProjectId",
            table: "BaseProject_ProductGroups",
            column: "BaseProjectId",
            principalTable: "BaseProject",
            principalColumn: "Id");

        migrationBuilder.AddForeignKey(
            name: "FK_QCPeriod_QCProjects_QCProjectId",
            table: "QCPeriod",
            column: "QCProjectId",
            principalTable: "QCProjects",
            principalColumn: "Id",
            onDelete: ReferentialAction.Cascade);
    }
}
