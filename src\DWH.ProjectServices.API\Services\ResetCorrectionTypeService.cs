﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Services.Interfaces;

namespace DWH.ProjectServices.API.Services;

public class ResetCorrectionTypeService : IResetCorrectionTypeService
{
    private readonly IResetCorrectionTypeRepository _resetCorrectionTypeRepository;

    public ResetCorrectionTypeService(IResetCorrectionTypeRepository resetCorrectionTypeRepository)
    {
        _resetCorrectionTypeRepository = resetCorrectionTypeRepository;
    }

    public async Task<IReadOnlyCollection<ResetCorrectionTypes>> GetAllAsync()
    {
        var result = await _resetCorrectionTypeRepository.GetAllAsync();

        return result;
    }
}
