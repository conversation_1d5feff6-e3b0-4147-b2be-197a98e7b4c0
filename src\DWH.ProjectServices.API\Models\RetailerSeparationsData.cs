﻿using DWH.ProjectServices.API.Models.Interfaces;

namespace DWH.ProjectServices.API.Models;

public class RetailerSeparationsData : IProjectServicesData
{
    public Guid Id { get; set; }
    public object SyncingEntityId { get; set; }
    public int BaseProjectId { get; set; }
    public int TypeId { get; set; }
    public int RetailerSeparationRequestId { get; set; }
    public string username { get; set; }
    public int IndexSourceBP { get; set; }
    public int TotalSourceBP { get; set; }
}
