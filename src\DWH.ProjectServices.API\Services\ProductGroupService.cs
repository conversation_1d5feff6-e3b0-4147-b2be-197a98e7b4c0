﻿using AutoMapper;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Models.Dtos;
using DWH.ProjectServices.API.Services.Interfaces;

namespace DWH.ProjectServices.API.Services;

public class ProductGroupService : IProductGroupService
{
    private readonly IMapper _mapper;
    private readonly IProductGroupRepository _productGroupRepository;

    public ProductGroupService(IMapper mapper, IProductGroupRepository productGroupRepository)
    {
        _productGroupRepository = productGroupRepository;
        _mapper = mapper;
    }

    public async Task<IEnumerable<ProductGroupResponse>> GetAllAsync(ProductGroupRequest productGroupRequestDto)
    {
        var productGroups = await _productGroupRepository.GetAllAsync(productGroupRequestDto);
        var result = _mapper.Map<ICollection<ProductGroupResponse>>(productGroups);
        return result;
    }

    public async Task<DescriptionResponse> GetDescriptionAsync(DescriptionRequest productGroupDto)
    {
        var periodicityDesc = _productGroupRepository.GetPeriodicityDesc(productGroupDto.PeriodicityId);
        var domainPGs = await _productGroupRepository.GetDomainProductGroupAsync(productGroupDto);
        var productSectorResponseDto = new DescriptionResponse(periodicityDesc, domainPGs);

        return productSectorResponseDto;
    }
}
