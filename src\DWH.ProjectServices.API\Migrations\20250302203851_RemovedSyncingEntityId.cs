﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DWH.ProjectServices.API.Migrations;

/// <inheritdoc />
public partial class RemovedSyncingEntityId : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropColumn(
            name: "SyncingEntityId",
            table: "OutBoxItem");

        migrationBuilder.AlterColumn<DateTimeOffset>(
            name: "Created<PERSON><PERSON>",
            table: "QCPeriod",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(2025, 3, 2, 20, 38, 50, 789, DateTimeKind.Unspecified).AddTicks(2049), new TimeSpan(0, 0, 0, 0, 0)),
            oldClrType: typeof(DateTimeOffset),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTimeOffset(new DateTime(2025, 2, 17, 9, 47, 15, 928, DateTimeKind.Unspecified).AddTicks(6390), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AlterColumn<string>(
            name: "Status",
            table: "OutBoxItem",
            type: "varchar(20)",
            nullable: false,
            oldClrType: typeof(string),
            oldType: "text");

        migrationBuilder.AlterColumn<DateTimeOffset>(
            name: "CreatedWhen",
            table: "BaseProject",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(2025, 3, 2, 20, 38, 50, 789, DateTimeKind.Unspecified).AddTicks(1315), new TimeSpan(0, 0, 0, 0, 0)),
            oldClrType: typeof(DateTimeOffset),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTimeOffset(new DateTime(2025, 2, 17, 9, 47, 15, 928, DateTimeKind.Unspecified).AddTicks(5650), new TimeSpan(0, 0, 0, 0, 0)));
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.AlterColumn<DateTimeOffset>(
            name: "CreatedWhen",
            table: "QCPeriod",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(2025, 2, 17, 9, 47, 15, 928, DateTimeKind.Unspecified).AddTicks(6390), new TimeSpan(0, 0, 0, 0, 0)),
            oldClrType: typeof(DateTimeOffset),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTimeOffset(new DateTime(2025, 3, 2, 20, 38, 50, 789, DateTimeKind.Unspecified).AddTicks(2049), new TimeSpan(0, 0, 0, 0, 0)));

        migrationBuilder.AlterColumn<string>(
            name: "Status",
            table: "OutBoxItem",
            type: "text",
            nullable: false,
            oldClrType: typeof(string),
            oldType: "varchar(20)");

        migrationBuilder.AddColumn<int>(
            name: "SyncingEntityId",
            table: "OutBoxItem",
            type: "integer",
            nullable: false,
            defaultValue: 0);

        migrationBuilder.AlterColumn<DateTimeOffset>(
            name: "CreatedWhen",
            table: "BaseProject",
            type: "timestamp with time zone",
            nullable: false,
            defaultValue: new DateTimeOffset(new DateTime(2025, 2, 17, 9, 47, 15, 928, DateTimeKind.Unspecified).AddTicks(5650), new TimeSpan(0, 0, 0, 0, 0)),
            oldClrType: typeof(DateTimeOffset),
            oldType: "timestamp with time zone",
            oldDefaultValue: new DateTimeOffset(new DateTime(2025, 3, 2, 20, 38, 50, 789, DateTimeKind.Unspecified).AddTicks(1315), new TimeSpan(0, 0, 0, 0, 0)));
    }
}
