﻿using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using FluentAssertions;

namespace DWH.ProjectServices.API.UnitTests.Models;

public class PeriodUTest
{
    [Fact]
    public void When_ValidParameters_Expect_NotNullInstance()
    {
        // Arrange
        var instance = new PeriodEntity
        {
            Id = 1,
            RefProjectId = 1,
            RefPeriodId = 1,
            QCPeriodId = 2
        };

        // Act & Assert
        instance.Should().NotBeNull();
    }
}
