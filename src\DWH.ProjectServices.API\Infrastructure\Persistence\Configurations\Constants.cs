﻿namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations;

public static class Constants
{
    public const string ADM_PG_PRODUCTGROUP = nameof(ADM_PG_PRODUCTGROUP);
    public const string ADM_PE_PERIODICITY = nameof(ADM_PE_PERIODICITY);
    public const string ADM_PG_DOMAIN_PRODUCTGROUP = nameof(ADM_PG_DOMAIN_PRODUCTGROUP);
    public const string ADM_PG_Sector = nameof(ADM_PG_Sector);
    public const string ZZZ_SUBPROJECTTYPE = nameof(ZZZ_SUBPROJECTTYPE);
    public const string ADM_PRJ_BASEPRODPROJECT = nameof(ADM_PRJ_BASEPRODPROJECT);
    public const string ADM_PRJ_BASEREPPROJECT = nameof(ADM_PRJ_BASEREPPROJECT);
    public const string ADM_PRJ_RBPROJECT_BASEPROJECT = nameof(ADM_PRJ_RBPROJECT_BASEPROJECT);
    public const string ADM_PRJ_QCPROJECT_PERIOD = nameof(ADM_PRJ_QCPROJECT_PERIOD);
    public const string ADM_PRJ_PRODPROJECT = nameof(ADM_PRJ_PRODPROJECT);
    public const string ADM_PRJ_REPPROJECT = nameof(ADM_PRJ_REPPROJECT);
    public const string ADM_PRJ_RBPROJECT = nameof(ADM_PRJ_RBPROJECT);
    public const string DWH_META = nameof(DWH_META);
    public const string DWH_DATA = nameof(DWH_DATA);
    public const string ADM_PG_PROJECT = nameof(ADM_PG_PROJECT);
    public const string FACT_PD_OUT_ITM = nameof(FACT_PD_OUT_ITM);
    public const string ADM_PRJ_RBPROJECT_DOMAIN_PG = nameof(ADM_PRJ_RBPROJECT_DOMAIN_PG);
    public const string ADM_PRJ_PANELTYPE = nameof(ADM_PRJ_PANELTYPE);
}
