﻿using DWH.ProjectServices.API.Models.Dtos;
using DWH.ProjectServices.API.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace DWH.ProjectServices.API.Presentation.Controllers;

public class ProductGroupsController : ApiController
{
    private readonly IProductGroupService _productGroupService;

    public ProductGroupsController(IProductGroupService productGroupService)
    {
        _productGroupService = productGroupService;
    }

    /// <summary>
    /// Gets ProductGroups filtered by CountryId, SectorId, CategoryId and PanelId
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(IEnumerable<ProductGroupResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetAsync(ProductGroupRequest productGroupRequestDto)
    {
        var result = await _productGroupService.GetAllAsync(productGroupRequestDto);
        return OkOrEmptyList(result);
    }

    /// <summary>
    /// Get ProductGroupSDesc, SectorSDesc and PeriodicitySDesc
    /// </summary>
    [HttpPost("description")]
    [ProducesResponseType(typeof(DescriptionResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetAutoGenerateDescription(DescriptionRequest productGroupDto)
    {
        var result = await _productGroupService.GetDescriptionAsync(productGroupDto);
        return OkOrEmpty(result);
    }
}
