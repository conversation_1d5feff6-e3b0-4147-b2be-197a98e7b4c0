﻿using System.Collections.Immutable;

namespace DWH.ProjectServices.API.Infrastructure.WebServiceClient;

public class Dictionary
{
    private const int Weekly = 52;
    private const int Monthly = 12;
    private const int BiMonthly = 6;
    private const int Quarterly = 3;
    private const int Annually = 1;
    private const int FiveTimesYearly = 5;
    private const int FourTimesYearly = 4;
    private const int TwoTimesYearly = 2;
    private const int ThreeTimesYearly = 3;

    public static readonly ImmutableDictionary<int, int> PeriodicityMapping = new Dictionary<int, int>
    {
        { 2, Weekly },
        { 4, Monthly },
        { 5, BiMonthly },
        { 6, Quarterly },
        { 7, Annually },
        { 8, FiveTimesYearly },
        { 9, FiveTimesYearly },
        { 16, Annually },
        { 17, FourTimesYearly },
        { 18, Annually },
        { 19, TwoTimesYearly },
        { 21, TwoTimesYearly },
        { 22, Annually },
        { 23, TwoTimesYearly },
        { 24, Annually },
        { 25, Annually },
        { 26, TwoTimesYearly },
        { 27, ThreeTimesYearly },
        { 28, Annually },
        { 29, Annually },
        { 30, Annually },
        { 31, Annually },
        { 32, Annually },
        { 33, Annually },
        { 34, Quarterly }
    }.ToImmutableDictionary();
}
