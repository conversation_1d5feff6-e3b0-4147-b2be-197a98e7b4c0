﻿using System.ComponentModel.DataAnnotations;
using AutoMapper;
using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Constants;
using DWH.ProjectServices.API.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace DWH.ProjectServices.API.Presentation.Controllers;

public class RetailerSeperationController : ApiController
{
    private readonly IMapper _mapper;
    private readonly IRetailerSeperationService _retailerSeperationService;
    private readonly IBaseProjectService _baseProjectService;

    public RetailerSeperationController(IMapper mapper, IRetailerSeperationService retailerSeperationService,
        IBaseProjectService baseProjectService)
    {
        _mapper = mapper;
        _retailerSeperationService = retailerSeperationService;
        _baseProjectService = baseProjectService;
    }

    /// <summary>
    /// Create a new Retailer Seperation Request
    /// </summary>
    [HttpPost]
    [Consumes("application/json")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(typeof(RetailerSeperationResponse), StatusCodes.Status201Created)]
    public async Task<IActionResult> AddAsync(
        [Required][FromHeader(Name = "userName")] string userName,
        [FromHeader(Name = "Email")] string email,
        RetailerSeperationCreateRequest retailerSeperationCreateRequest)
    {
        try
        {
            RetailerSeperationRequestDetailModelDto details = new RetailerSeperationRequestDetailModelDto();
            details.UpdatedBy = userName;
            details.RequestStatusId = (int)RetailerRequestConstants.Pending;
            retailerSeperationCreateRequest.RetailerSeperationRequestDetails = new List<RetailerSeperationRequestDetailModelDto>() { details };
            retailerSeperationCreateRequest.RequestStatusId = (int)RetailerRequestConstants.Pending;
            var retailerSeperation = _mapper.Map<RetailerSeperationRequest>(retailerSeperationCreateRequest);
            var result = await _retailerSeperationService.AddAsync(retailerSeperation, email);
            var resultResponse = _mapper.Map<RetailerSeperationResponse>(result);

            if (result != null)
            {
                return CreatedAtAction(null, resultResponse);
            }

            return BadRequest(resultResponse);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Gets all RetailerSeperations filtered or unfiltered
    /// </summary>
    [HttpPost("list")]
    [Consumes("application/json")]
    [EndpointSummary("Gets all retailerSeperations filtered or unfiltered")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(IReadOnlyList<RetailerSeperationListResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetAsyncList([FromBody] RetailerSeperationListRequest retailerSeperationListRequest)
    {
        var retailerSeperationList = _mapper.Map<RetailerSeperationsLists>(retailerSeperationListRequest);
        var result = await _retailerSeperationService.GetAsyncList(retailerSeperationList);
        var response = _mapper.Map<RetailerSeperationListResponse>(result);
        return OkOrEmpty(response);
    }

    /// <summary>
    /// Gets user list of retailerseperations
    /// </summary>
    [HttpGet("Userlist")]
    [Consumes("application/json")]
    [EndpointSummary("Gets all retailerSeperations filtered or unfiltered")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(IReadOnlyList<string>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetUserList()
    {
        var result = await _retailerSeperationService.GetUsersList();
        return OkOrEmpty(result);
    }

    /// <summary>
    /// Performs I/R Separation 
    /// </summary>
    [HttpPost("IRSeparation")]
    [Consumes("application/json")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(typeof(IRSeparationRecords), StatusCodes.Status207MultiStatus)]
    public async Task<IActionResult> CreateAsyncIRSeparation([FromHeader(Name = "userName")] string userName,
        [FromBody] IRSeparationRequest irSeparationRequest)
    {
        var response = new IRSeparationRecords
        {
            IRSeparationRecord = new List<IRSeparationResponse>()
        };

        foreach (var retailerSeperationRequestId in irSeparationRequest.retailerSeperationRequestIds)
        {
            var sourceBaseProjects = await _retailerSeperationService.GetSourceBaseProjects(retailerSeperationRequestId);
            var TotalSourceBP = sourceBaseProjects.Count();
            var indexSourceBP = 0;
            foreach (var baseProjectId in sourceBaseProjects)
            {
                indexSourceBP++;
                await _retailerSeperationService.PerformRetailerSeparationAsyncToRabbitMQ(baseProjectId, (int)ProjectSubType.Industry, userName, retailerSeperationRequestId, indexSourceBP, TotalSourceBP);
            }
            RetailerSeperationRequestDetailModelDto details = new RetailerSeperationRequestDetailModelDto
            {
                RetailerSeperationRequestId = retailerSeperationRequestId,
                RequestStatusId = (int)RetailerRequestConstants.Executing,
                UpdatedBy = userName,
                UpdatedWhen = DateTimeOffset.UtcNow,
            };

            var retailerSeperationDetails = _mapper.Map<RetailerSeperationRequestDetail>(details);
            await _retailerSeperationService.AddDetailAndUpdateStatusAsync(retailerSeperationDetails);
            var irSeparationResponse = new IRSeparationResponse
            {
                RetailerSeperationRequestId = retailerSeperationRequestId
            };
            response.IRSeparationRecord.Add(irSeparationResponse);
        }

        return StatusCode(StatusCodes.Status207MultiStatus, response);
    }

    /// <summary>
    /// Performs I/R Separation on a single base project
    /// </summary>
    [HttpPost("IRSeparationBaseProject")]
    [Consumes("application/json")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    [ProducesResponseType(typeof(IRSeparationResponseBaseProject), StatusCodes.Status207MultiStatus)]
    public async Task<IActionResult> CreateAsyncIRSeparationBaseProject([FromHeader(Name = "userName")] string userName,
         IRSeparationBaseProjectRequest irSeparationBaseProject)
    {
        if (irSeparationBaseProject.indexSourceBP == 1)
        {
            await _retailerSeperationService.UpdateJiraTicketAsync(irSeparationBaseProject.retailerSeparationId, ticketStatus: "In Progress");
        }

        if (irSeparationBaseProject.indexSourceBP == irSeparationBaseProject.totalSourceBP)
        {
            await _retailerSeperationService.UpdateJiraTicketAsync(irSeparationBaseProject.retailerSeparationId, comment: "BX backend processing is completed, and the csv template is available for downloading in Builder X.");
        }

        var retailerBPId = await _retailerSeperationService.PerformRetailerSeparationAsync(
            irSeparationBaseProject.SourceBaseProjectId,
            (int)ProjectSubType.Industry,
            userName,
            irSeparationBaseProject.retailerSeparationId,
            irSeparationBaseProject.indexSourceBP,
            irSeparationBaseProject.totalSourceBP
        );
        var response = new IRSeparationResponseBaseProject()
        {
            RetailerSeperationRequestId = irSeparationBaseProject.retailerSeparationId,
            IndustryProject = irSeparationBaseProject.SourceBaseProjectId,
            RetailerProject = retailerBPId
        };

        return StatusCode(StatusCodes.Status200OK, response);
    }

    /// <summary>
    /// Gets a single RetailerSeparationRequest by Id
    /// </summary>
    [HttpGet("{requestId}")]
    [Consumes("application/json")]
    [ActionName(nameof(GetAsync))]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(RetailerSeparationGetResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetAsync(int requestId)
    {
        var retailerSeperationRequests = await _retailerSeperationService.GetAsync(requestId);
        var result = _mapper.Map<RetailerSeparationGetResponse>(retailerSeperationRequests);
        return OkOrEmpty(result);
    }

    /// <summary>
    /// Update Retailer Separation Request
    /// </summary>
    [HttpPut("{requestId}")]
    [Consumes("application/json")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(RetailerSeparationEditResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> UpdateAsync([FromHeader(Name = "Custom-Countryid")] string countryIds,
    [Required] [FromHeader(Name = "userName")]
    string userName, [Required] [FromHeader(Name = "Email")]
    string userEmail, int requestId, RetailerSeperationEditRequest retailerSeperationEditRequest)
    {
        if (!string.IsNullOrEmpty(countryIds))
        {
            var retailerseparationCountryModel = new RetailerSeparationCountries();
            retailerseparationCountryModel.CountryIds = countryIds.Split(',').Select(int.Parse).ToList();
            retailerseparationCountryModel.RetailerSeperationIds = retailerSeperationEditRequest.RetailerSeperations
                                        .Select(item => item.SourceBPId)
                                        .ToList();

            var countryResult = await _retailerSeperationService.GetAuthorizedBaseProjectsByCountry(retailerseparationCountryModel);
            if (countryResult.Count == 0)
            {
                return StatusCode(StatusCodes.Status403Forbidden);
            }
        }

        var retailerseparationRequest = new RetailerSeperationRequest
        {
            Id = requestId,
            FromPeriodId = retailerSeperationEditRequest.FromPeriodId,
            ToPeriodId = retailerSeperationEditRequest.ToPeriodId,
            ResetCorrection = retailerSeperationEditRequest.ResetCorrection,
            Extrapolation = retailerSeperationEditRequest.Extrapolation,
            RetailerSeperations = retailerSeperationEditRequest.RetailerSeperations.Select(item => new RetailerSeperation
            {
                SourceBPId = item.SourceBPId,
                IsError = item.IsError
            }).ToList()
        };

        var errorDetails = retailerSeperationEditRequest.RetailerSeperations.FirstOrDefault()?.ErrorDetails ?? string.Empty;
        var response = await _retailerSeperationService.UpdateAsync(userName, userEmail, retailerseparationRequest, errorDetails);
        var result = _mapper.Map<RetailerSeparationEditResponse>(response);
        return OkOrEmpty(result);

    }

    /// <summary>
    /// Update Status of Retailer Separation Request
    /// </summary>
    [HttpPut("status")]
    [Consumes("application/json")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(RetailerSeperationStatusResponse), StatusCodes.Status207MultiStatus)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> UpdateStatusAsync(
    [Required] [FromHeader(Name = "userName")]
    string userName, [Required] [FromHeader(Name = "Email")]
    string userEmail, RetailerSeperationStatusRequest retailerSeperationStatusRequest)
    {
        var response = new RetailerSeperationStatusResponse()
        {
            status = new List<bool>()
        };

        foreach (var retailerseparationRequestId in retailerSeperationStatusRequest.retailerSeperationRequestIds)
        {
            var retailerseparationStatusDetails = new RetailerSeparationStatusDetails
            {
                RequestId = retailerseparationRequestId,
                StatusId = retailerSeperationStatusRequest.statusId,
                Reason = retailerSeperationStatusRequest.reason,
                Username = userName,
                UserEmail = userEmail,
            };
            var result = await _retailerSeperationService.UpdateStatusAsync(retailerseparationStatusDetails);
            response.status.Add(result);
        }
        return StatusCode(StatusCodes.Status207MultiStatus, response);
    }

    /// <summary>
    /// Delete Source BP in an IR Request
    /// </summary>
    [HttpDelete()]
    [Consumes("application/json")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(IReadOnlyList<ResponseInfoRetailerSeperation>), StatusCodes.Status207MultiStatus)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> DeleteAsyncSourceBP(
        [FromHeader(Name = "Email")] string email,
        [FromHeader(Name = "Custom-Countryid")] string countryIds,
        IRSeparationSourceBaseprojectsDeleteRequest deleteIRSeperationRequest)
    {
        if (!deleteIRSeperationRequest.RetailerSeperationIds.Any())
            return BadRequest("Atleast one retailer seperation id is required");
        // Filter IR Separations by Country
        var authorizedSourceBPIRSeperations = await FilterBaseProjectsByCountry(countryIds, deleteIRSeperationRequest.RetailerSeperationIds);

        if (!authorizedSourceBPIRSeperations.Any())
            return StatusCode(StatusCodes.Status403Forbidden);
        deleteIRSeperationRequest.Email = email;
        deleteIRSeperationRequest.RetailerSeperationIds = authorizedSourceBPIRSeperations.ToList();

        var IRSeperationDelete = _mapper.Map<IRSeperationDeletes>(deleteIRSeperationRequest);
        IReadOnlyList<ResponseInfoRetailerSeperation> responses =
            await _retailerSeperationService.DeleteSourceBPAsync(IRSeperationDelete);

        return StatusCode(StatusCodes.Status207MultiStatus, responses);
    }

    private async Task<IReadOnlyList<int>> FilterBaseProjectsByCountry(string countryIds, IEnumerable<int> retailerSeperationids)
    {
        if (string.IsNullOrEmpty(countryIds))
            return retailerSeperationids.ToList();

        var retailerSeperationCountryModel = new RetailerSeparationCountries
        {
            CountryIds = countryIds.Split(',').Select(int.Parse).ToList(),
            RetailerSeperationIds = retailerSeperationids.ToList()
        };

        var countryResult = await _retailerSeperationService.GetAuthorizedRetailerSeperationsByCountry(retailerSeperationCountryModel);


        return countryResult.Any() ? countryResult : Array.Empty<int>();
    }


}
